package algorithm

import (
	"fmt"
	"math"
	"math/rand"
	"time"

	"gorgonia.org/gorgonia"
	"gorgonia.org/tensor"
)

//In window, set environment like this:
//	$env:ASSUME_NO_MOVING_GC_UNSAFE_RISK_IT_WITH="go1.23"

type Matrix struct {
	graph *gorgonia.ExprGraph
	shape int
}

func MakeMatrix(shape int) *Matrix {

	g := gorgonia.NewGraph()

	return &Matrix{graph: g, shape: shape}
}

func (m *Matrix) GetRandom() *tensor.Dense {
	rand.Seed(time.Now().UnixNano())

	randomData := tensor.New(
		tensor.WithShape(m.shape, m.shape),
		tensor.WithBacking(tensor.Random(tensor.Float64, m.shape*m.shape)),
	)

	return randomData
}

func (m *Matrix) Calculate(randomData *tensor.Dense, rounds int) (*tensor.Dense, error) {
	a := gorgonia.NewTensor(m.graph, tensor.Float64, 2, gorgonia.WithShape(m.shape, m.shape), gorgonia.WithValue(randomData))
	b := gorgonia.NewTensor(m.graph, tensor.Float64, 2, gorgonia.WithShape(m.shape, m.shape), gorgonia.WithValue(randomData))

	current := a
	for i := 0; i < rounds; i++ {
		current, err := gorgonia.Mul(current, b)
		if err != nil {
			return nil, err
		}

		machine := gorgonia.NewTapeMachine(m.graph)
		gorgonia.Compile(m.graph)
		if err := machine.RunAll(); err != nil {
			machine.Close()
			return nil, err
		}

		result := current.Value().(*tensor.Dense)
		hashed, err := applyHash(result)
		if err != nil {
			machine.Close()
			return nil, err
		}

		current = gorgonia.NewTensor(m.graph, tensor.Float64, 2, gorgonia.WithValue(hashed), gorgonia.WithName(fmt.Sprintf("Hashed_%d", i+1)))
		machine.Close()
	}

	return current.Value().(*tensor.Dense), nil
}

func hashFunction(x float64) float64 {
	return float64(math.Sin(float64(x)) + math.Cos(float64(x*2)))
}

func applyHash(mat *tensor.Dense) (*tensor.Dense, error) {
	data := mat.Data().([]float64)
	hashedData := make([]float64, len(data))
	for i, v := range data {
		hashedData[i] = hashFunction(v)
	}
	return tensor.New(tensor.WithShape(mat.Shape()...), tensor.WithBacking(hashedData)), nil
}
