package main

import (
	"fmt"
)

func main() {
	matrix := [3][3]int{
		{3, 1, -1},
		{2, -8, 1},
		{-1, 3, 2},
	}

	result := [3]int{1, -2, 3}
	estimate := [3]float32{0, 0, 0}
	middle := [3]float32{}

	for i := 0; i < 100; i++ {
		for index, value := range matrix {
			remain := float32(0)
			sum := float32(0)
			for index1, value1 := range value {
				if index == index1 {
					remain = float32(value1)
				} else {
					sum += estimate[index1] * float32(value1)
				}
			}
			middle[index] = (float32(result[index]) - sum) / remain
		}

		estimate = middle
	}

	fmt.Println(estimate)
}
