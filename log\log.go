package log

import (
	"fmt"
	"os"
	"path/filepath"

	"github.com/ipfs/go-log/v2"
	"uptech.ai/ainode/config"
)

func InitLogger(config *config.Config) error {
	logConfig := log.GetConfig()
	if config.LogLevel != "" {
		logLevel, err := log.LevelFromString(config.LogLevel)
		if err != nil {
			return err
		}

		logConfig.Level = logLevel
	} else {
		logConfig.Level = log.LevelDebug
	}

	logConfig.Stderr = false
	logConfig.Stdout = false

	if config.LogPath == "" {
		workingDir, err := os.Getwd()
		if err != nil {
			fmt.Println("Error:", err)
			return err
		}

		logConfig.File = filepath.Join(workingDir, "running.log")
	} else {
		logConfig.File = filepath.Join(config.LogPath, "running.log")
	}

	log.SetupLogging(logConfig)

	return nil
}
