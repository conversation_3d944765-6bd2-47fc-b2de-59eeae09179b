package main

import (
	"bufio"
	"context"
	"flag"
	"fmt"
	"os"
	"strings"

	logging "github.com/ipfs/go-log/v2"
	"github.com/libp2p/go-libp2p/core/host"
	"github.com/libp2p/go-libp2p/core/peer"
	"uptech.ai/ainode/config"
	"uptech.ai/ainode/log"
	"uptech.ai/ainode/node"
	"uptech.ai/ainode/protocol"
)

var logger = logging.Logger("chat tool")

func main() {
	configFilePath := flag.String("config", "", "config file path")
	flag.Parse()

	config, err := config.LoadConfig(*configFilePath)
	if err != nil {
		panic(err)
	}

	err = log.InitLogger(config)
	if err != nil {
		panic(err)
	}

	ctx := context.Background()
	nodeImpl, err := node.MakeHostNode(ctx, config)
	if err != nil {
		panic(err)
	}

	nodeImpl.Run(ctx)

	err = node.MakeNATGossip(ctx, nodeImpl, config.NATIP, config.NATPort)
	if err != nil {
		panic(err)
	}

	startChat(ctx, nodeImpl)
}

func startChat(ctx context.Context, h host.Host) {
	reader := bufio.NewReader(os.Stdin)

	for {
		fmt.Println("Enter target peerID:")
		targetAddrStr, err := reader.ReadString('\n')
		if err != nil {
			logger.Errorf("Stdin closed. Exiting...")
			break
		}

		targetAddrStr = strings.TrimSpace(targetAddrStr)
		peerId, err := peer.Decode(targetAddrStr)
		if err != nil {
			logger.Errorf("Failed to decode peer: %v", err)
			continue
		}

		fmt.Println("You can start chatting now! Type your messages below:")

		fmt.Print("> ")
		msg, err := reader.ReadString('\n')
		if err != nil {
			logger.Errorf("Stdin closed. Exiting...")
			break
		}

		err = protocol.Rawprotocol.SendProtocol(ctx, h, peerId, msg)
		if err != nil {
			logger.Errorf("Failed to send protocal %v", err)
		}
	}
}
