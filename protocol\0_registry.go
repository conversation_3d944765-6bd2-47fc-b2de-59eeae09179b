package protocol

import (
	"context"

	"github.com/libp2p/go-libp2p/core/host"
	"github.com/libp2p/go-libp2p/core/network"
	"github.com/libp2p/go-libp2p/core/peer"
	"github.com/libp2p/go-libp2p/core/protocol"
)

var ProtocolHandlers = make(map[protocol.ID]func(network.Stream))

func RegisterHandler(protocolID protocol.ID, handler func(network.Stream)) {
	ProtocolHandlers[protocolID] = handler
}

type (
	Protocol interface {
		HandleProtocol(s network.Stream)
		SendProtocol(ctx context.Context, h host.Host, peerId peer.ID, msg string) error
	}
)
