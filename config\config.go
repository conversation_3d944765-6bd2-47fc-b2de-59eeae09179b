package config

import (
	"fmt"
	"os"

	"gopkg.in/yaml.v3"
)

/**
port: 8080
bootstrap:
- /ip4/127.0.0.1/tcp/9000/p2p/QmBootstrapPeer1
- /ip4/*************/tcp/9001/p2p/QmBootstrapPeer2
*/

type Config struct {
	BootstrapPeers []string `yaml:"bootstrap"` // Bootstrap 地址列表
	Port           int      `yaml:"port"`      // 端口
	ListenIP       string   `yaml:"listen"`    // 本地地址
	Id             string   `yaml:"id"`
	PriKey         string   `yaml:"private"`
	PubKey         string   `yaml:"public"`
	LogLevel       string   `yaml:"loglevel"`
	LogPath        string   `yaml:"logpath"`
	WalletKey      string   `yaml:"walletkey"`
	ChainUrl       string   `yaml:"chainurl"`
	ChainID        int      `yaml:"chainid"`
	Contract       string   `yaml:"contract"`
	FromHeight     int64    `yaml:"fromheight"`
	Stake          int64    `yaml:"stake"`
	EnableRelaySvc bool     `yaml:"enableRelaySvc"`
	NATIP          string   `yaml:"natIP"`   // 端口
	NATPort        int      `yaml:"natPort"` // 本地地址
}

func LoadConfig(filename string) (*Config, error) {
	file, err := os.Open(filename)
	if err != nil {
		return nil, err
	}
	defer file.Close()

	var config Config
	decoder := yaml.NewDecoder(file)
	if err := decoder.Decode(&config); err != nil {
		return nil, err
	}

	if len(config.BootstrapPeers) == 0 {
		return nil, fmt.Errorf("missing required field: bootstrap")
	}

	if config.Port == 0 {
		return nil, fmt.Errorf("missing or invalid required field: port")
	}

	if config.ListenIP == "" {
		return nil, fmt.Errorf("missing or invalid required field: listen")
	}

	if config.Id == "" {
		return nil, fmt.Errorf("missing or invalid required field: id")
	}

	if config.PriKey == "" {
		return nil, fmt.Errorf("missing or invalid required field: undefined")
	}

	if config.PubKey == "" {
		return nil, fmt.Errorf("missing or invalid required field: public")
	}

	if config.WalletKey == "" {
		return nil, fmt.Errorf("missing or invalid required field: walletkey")
	}

	if config.ChainUrl == "" {
		return nil, fmt.Errorf("missing or invalid required field: chainurl")
	}

	if config.ChainID == 0 {
		return nil, fmt.Errorf("missing or invalid required field: chainid")
	}

	if config.Contract == "" {
		return nil, fmt.Errorf("missing or invalid required field: contract")
	}

	return &config, nil
}
