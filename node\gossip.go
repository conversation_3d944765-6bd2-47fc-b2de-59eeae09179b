package node

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	pubsub "github.com/libp2p/go-libp2p-pubsub"
	"github.com/libp2p/go-libp2p/core/host"
	"github.com/libp2p/go-libp2p/core/peer"
	"github.com/libp2p/go-libp2p/core/peerstore"

	ma "github.com/multiformats/go-multiaddr"
)

const (
	gossipTopic = "static-nat"
)

type StaticNatMessage struct {
	PeerId string
	Addr   string
}

func MakeNATGossip(ctx context.Context, host host.Host, natIP string, natPort int) error {
	ps, err := pubsub.NewGossipSub(ctx, host)
	if err != nil {
		return err
	}

	topic, err := ps.Join(gossipTopic)
	if err != nil {
		return err
	}

	sub, err := topic.Subscribe()
	if err != nil {
		return err
	}

	go func() {
		for {
			msg, err := sub.Next(ctx)
			if err != nil {
				logger.Errorf("Fail to recv message: %v", err)
				return
			}

			// only forward messages delivered by others
			if msg.ReceivedFrom == host.ID() {
				continue
			}

			staticMessage := new(StaticNatMessage)
			err = json.Unmarshal(msg.GetData(), staticMessage)
			if err != nil {
				logger.Errorf("Fail to unmarshal %v", err)
				continue
			}

			logger.Debugf("Recv message:%s, recv from: %s", string(msg.Data), msg.ReceivedFrom.String())
			nat, _ := ma.NewMultiaddr(staticMessage.Addr)
			id, _ := peer.Decode(staticMessage.PeerId)
			host.Peerstore().AddAddr(id, nat, peerstore.AddressTTL)
		}
	}()

	if natPort != 0 {
		return nil
	}

	go func() {
		ticker := time.NewTicker(30 * time.Second)
		defer ticker.Stop()
		for {
			select {
			case <-ticker.C:
				m := StaticNatMessage{
					PeerId: host.ID().String(),
					Addr:   fmt.Sprintf("/ip4/%s/tcp/%d", natIP, natPort),
				}
				msgBytes, err := json.Marshal(m)
				if err != nil {
					logger.Errorf("Fail to mashal static nat message: %v", err)
					break
				}
				topic.Publish(ctx, msgBytes)
				logger.Debugf("Public:%s", msgBytes)
			case <-ctx.Done():
				return
			}
		}
	}()

	return nil

}
