package util

import (
	"github.com/libp2p/go-libp2p/core/crypto"
	"github.com/libp2p/go-libp2p/core/peer"
)

func MakeAndMarshalKeyPair() (privateKey string, publicKey string, id string, err error) {
	priv, pub, err := crypto.GenerateKeyPair(crypto.Secp256k1, 256)
	if err != nil {
		return "", "", "", err
	}

	// private key
	if mashalPriv, err := crypto.MarshalPrivateKey(priv); err != nil {
		return "", "", "", err
	} else {
		privateKey = crypto.ConfigEncodeKey(mashalPriv)
	}

	// public key
	if mashalPub, err := crypto.MarshalPublicKey(pub); err != nil {
		return "", "", "", err
	} else {
		publicKey = crypto.ConfigEncodeKey(mashalPub)
	}

	// id
	if idFromKey, err := peer.IDFromPublicKey(pub); err != nil {
		return "", "", "", err
	} else {
		id = idFromKey.String()
	}

	return privateKey, publicKey, id, nil
}

func UnmarshalKeyPair(publicKey string, privateKey string) (privKey crypto.PrivKey, pubKey crypto.PubKey, err error) {
	if priv, err := crypto.ConfigDecodeKey(privateKey); err != nil {
		return nil, nil, err
	} else {
		if privKey, err = crypto.UnmarshalPrivateKey(priv); err != nil {
			return nil, nil, err
		}
	}

	if pub, err := crypto.ConfigDecodeKey(publicKey); err != nil {
		return nil, nil, err
	} else {
		if pubKey, err = crypto.UnmarshalPublicKey(pub); err != nil {
			return nil, nil, err
		}
	}

	return privKey, pubKey, nil
}
