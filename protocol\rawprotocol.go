package protocol

import (
	"bufio"
	"context"
	"fmt"
	"time"

	logging "github.com/ipfs/go-log/v2"
	"github.com/libp2p/go-libp2p/core/host"
	"github.com/libp2p/go-libp2p/core/network"
	"github.com/libp2p/go-libp2p/core/peer"
	ma "github.com/multiformats/go-multiaddr"
)

const (
	protocolID = "/example/rawprotocol/1.0.0"
)

type RawProtocol struct {
	Protocol
}

var (
	logger      = logging.Logger("rawprotocol")
	Rawprotocol = RawProtocol{}
)

func init() {
	RegisterHandler(protocolID, Rawprotocol.HandleProtocol)
}

func (rawProtocal *RawProtocol) HandleProtocol(s network.Stream) {
	defer s.Close()
	rw := bufio.NewReadWriter(bufio.NewReader(s), bufio.NewWriter(s))

	if msg, err := rw.ReadString('\n'); err != nil {
		logger.Errorf("Fail to read message: %v", err)
		return
	} else {
		fmt.Println("Recv message: ", msg)
	}
}

func (rawProtocal *RawProtocol) SendProtocol(ctx context.Context, h host.Host, peerId peer.ID, msg string) error {
	// h.Network().(*swarm.Swarm).Backoff().Clear(peerId)

	// relayPeer, _ := ma.NewMultiaddr(fmt.Sprintf("/p2p/%s/p2p-circuit/", peerinfo.ID.String()))
	relayaddr, _ := ma.NewMultiaddr("/p2p/" + "16Uiu2HAmMX7aSuZG2KqXQD1QYR7KLSnkbFerGE384zjxssRre7rf" + "/p2p-circuit/p2p/" + peerId.String())

	peerAddr := peer.AddrInfo{
		ID:    peerId,
		Addrs: []ma.Multiaddr{relayaddr},
	}

	if err := h.Connect(context.Background(), peerAddr); err != nil {
		logger.Errorf("fail to connect relay: %v", err)
		return err
	}

	logger.Debugf("Connected to peer: %s, addrs:%s", peerId.String(), h.Peerstore().Addrs(peerId))

	stream, err := h.NewStream(ctx, peerId, protocolID)
	if err != nil {
		logger.Errorf("Failed to create stream: %v", err)
		return err
	}

	logger.Debugf("Local multiaddr:%s, remote mulitiaddr: %s", stream.Conn().LocalMultiaddr(), stream.Conn().RemoteMultiaddr())
	defer stream.Close()

	deadline := time.Now().Add(10 * time.Second)
	err = stream.SetWriteDeadline(deadline)
	if err != nil {
		logger.Errorf("Failed to set write deadline: %v", err)
		return err
	}

	_, err = stream.Write([]byte(msg))
	if err != nil {
		logger.Errorf("Failed to send message: %v", err)
		return err
	}

	return nil
}
