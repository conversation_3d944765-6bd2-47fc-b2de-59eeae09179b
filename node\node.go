package node

import (
	"context"
	"fmt"
	"math/rand"
	"strings"
	"time"

	logging "github.com/ipfs/go-log/v2"
	"github.com/libp2p/go-libp2p"
	dht "github.com/libp2p/go-libp2p-kad-dht"
	"github.com/libp2p/go-libp2p/core/event"
	"github.com/libp2p/go-libp2p/core/host"
	"github.com/libp2p/go-libp2p/core/network"
	"github.com/libp2p/go-libp2p/core/peer"
	drouting "github.com/libp2p/go-libp2p/p2p/discovery/routing"
	dutil "github.com/libp2p/go-libp2p/p2p/discovery/util"
	rhost "github.com/libp2p/go-libp2p/p2p/host/routed"
	"github.com/libp2p/go-libp2p/p2p/protocol/circuitv2/relay"
	"github.com/libp2p/go-libp2p/p2p/security/noise"
	ma "github.com/multiformats/go-multiaddr"

	"uptech.ai/ainode/config"
	"uptech.ai/ainode/protocol"
	"uptech.ai/ainode/util"
)

// node client version
const (
	clientVersion    = "go-p2p-node/0.0.1"
	rendezvousString = "my-rendezvous-point"
)

var logger = logging.Logger("node")

type Node struct {
	host.Host      // lib-p2p host
	bootStrapPeers []peer.AddrInfo
}

func MakeHostNode(ctx context.Context, config *config.Config) (*Node, error) {
	privKey, _, err := util.UnmarshalKeyPair(config.PubKey, config.PriKey)
	if err != nil {
		return nil, err
	}

	addr := fmt.Sprintf("/ip4/%s/tcp/%d", config.ListenIP, config.Port)
	logger.Debugf("my id is :%s, my address is: %s", config.Id, addr)

	bootstrapPeers := make([]peer.AddrInfo, len(config.BootstrapPeers))
	for i, addr := range config.BootstrapPeers {
		peerAddr, err := ma.NewMultiaddr(addr)
		if err != nil {
			return nil, err
		}

		peerinfo, _ := peer.AddrInfoFromP2pAddr(peerAddr)
		bootstrapPeers[i] = *peerinfo
	}

	listen, _ := ma.NewMultiaddr(fmt.Sprintf("/ip4/%s/tcp/%d", config.ListenIP, config.Port))

	opts := []libp2p.Option{
		libp2p.ListenAddrs(listen),
		libp2p.Identity(privKey),
		// libp2p.Security(libp2ptls.ID, libp2ptls.New),
		libp2p.Security(noise.ID, noise.New),
		libp2p.EnableRelay(),
	}

	if config.EnableRelaySvc {
		logger.Debugf("Relay service is enable")
		opts = append(opts, libp2p.EnableRelayService(relay.WithInfiniteLimits()), libp2p.EnableNATService(), libp2p.ForceReachabilityPublic())
	} else {
		opts = append(opts,
			libp2p.EnableAutoRelayWithStaticRelays(bootstrapPeers),
			libp2p.EnableAutoNATv2(),
			libp2p.NATPortMap())
	}

	logger.Debugf("Starting new p2p...")

	host, err := libp2p.New(opts...)
	if err != nil {
		return nil, err
	}

	logger.Debugf("My real id is %s", host.ID().String())

	logger.Debugf("Starting new dht...")
	dhtOpts := []dht.Option{
		dht.BootstrapPeers(bootstrapPeers...),
		dht.ProtocolPrefix("/uptech"),
		dht.Mode(dht.ModeServer),
	}

	kademliaDHT, err := dht.New(ctx, host, dhtOpts...)
	if err != nil {
		return nil, err
	}

	err = kademliaDHT.Bootstrap(ctx)
	if err != nil {
		logger.Errorf("Bootstrap failed: %v", err)
		return nil, err
	}

	logger.Debugf("Bootstrapping the DHT")

	// Make the routed host
	routedHost := rhost.Wrap(host, kademliaDHT)
	time.Sleep(1 * time.Second)

	logger.Debugf("Announcing ourselves...")
	routingDiscovery := drouting.NewRoutingDiscovery(kademliaDHT)
	dutil.Advertise(ctx, routingDiscovery, rendezvousString)
	logger.Debugf("Successfully announced!")

	go func() {
		ticker := time.NewTicker(1 * time.Minute)
		defer ticker.Stop()
		for {
			select {
			case <-ticker.C:
				peerChan, err := routingDiscovery.FindPeers(ctx, rendezvousString)
				if err != nil {
					continue
				}

				for peer := range peerChan {
					logger.Debug("Found peer:", peer)
				}

			case <-ctx.Done():
				return
			}
		}
	}()

	node := &Node{routedHost, bootstrapPeers}

	return node, nil
}

func (node *Node) Run(ctx context.Context) {
	node.monitorPeerEvents(ctx)
	node.refreshBootstrap(ctx)
	node.registerStreamHandlers()
}

func (node *Node) refreshBootstrap(ctx context.Context) {
	go func() {
		ticker := time.NewTicker(20 * time.Second)
		defer ticker.Stop()
		for {
			select {
			case <-ticker.C:
				peer := node.bootStrapPeers[rand.Intn(len(node.bootStrapPeers))]
				if strings.Compare(peer.ID.String(), node.ID().String()) != 0 {
					deadlineCtx, cancel := context.WithDeadline(ctx, time.Now().Add(10*time.Second))
					err := node.Connect(deadlineCtx, peer)
					cancel()
					if err != nil {
						logger.Errorf("Fail to connect to %s", peer.ID.String())
					} else {
						logger.Infof("Success to connect to %s", peer.ID.String())
					}
				}

			case <-ctx.Done():
				return
			}
		}
	}()
}

func (node *Node) registerStreamHandlers() {
	for id, handler := range protocol.ProtocolHandlers {
		node.SetStreamHandler(id, handler)
	}
}

func (node *Node) monitorPeerEvents(ctx context.Context) error {
	bus := node.EventBus()

	sub, err := bus.Subscribe([]interface{}{
		new(event.EvtPeerConnectednessChanged),
		new(event.EvtPeerIdentificationCompleted),
		new(event.EvtLocalReachabilityChanged),
		new(event.EvtNATDeviceTypeChanged),
		new(event.EvtPeerProtocolsUpdated),
		new(event.EvtLocalProtocolsUpdated),
	})
	if err != nil {
		return fmt.Errorf("failed to subscribe to peer events: %v", err)
	}

	go func() {
		for {
			select {
			case evt := <-sub.Out():
				if connEvent, ok := evt.(event.EvtPeerConnectednessChanged); ok {
					switch connEvent.Connectedness {
					case network.Connected:
						logger.Infof("Peer connected: %s", connEvent.Peer)
					case network.NotConnected:
						logger.Infof("Peer disconnected: %s", connEvent.Peer)
					case network.Limited:
						logger.Infof("Peer limited: %s", connEvent.Peer)
					}
				}

				if connEvent, ok := evt.(event.EvtPeerIdentificationCompleted); ok {
					logger.Infof("Oberved Addrs: %s", connEvent.ObservedAddr)
					addrs := node.Peerstore().Addrs(connEvent.Peer)
					for _, addr := range addrs {
						logger.Infof("Peer store addrs:%s", addr)
					}
					logger.Infof("Peer store addrs: %s", node.Peerstore().Addrs(connEvent.Peer))
					logger.Infof("Peer: %s", connEvent.Peer)
				}

				if connEvent, ok := evt.(event.EvtLocalReachabilityChanged); ok {
					logger.Infof("Reachbility: %s", connEvent.Reachability.String())
				}

				if connEvent, ok := evt.(event.EvtNATDeviceTypeChanged); ok {
					logger.Infof("NAT Type: %s", connEvent.NatDeviceType.String())
				}

				if connEvent, ok := evt.(event.EvtPeerProtocolsUpdated); ok {
					logger.Infof("Peer protocal: peer:%s, added:%s, delete:%s", connEvent.Peer.String(), connEvent.Added, connEvent.Removed)
				}

				if connEvent, ok := evt.(event.EvtLocalProtocolsUpdated); ok {
					logger.Infof("Local protocal: added:%s, delete: %s", connEvent.Added, connEvent.Removed)
				}

			case <-ctx.Done():
				sub.Close()
				return
			}
		}
	}()

	return nil
}
