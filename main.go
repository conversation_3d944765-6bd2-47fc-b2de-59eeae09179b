package main

import (
	"context"
	"flag"

	logging "github.com/ipfs/go-log/v2"
	"uptech.ai/ainode/config"
	"uptech.ai/ainode/log"
	"uptech.ai/ainode/node"
)

var logger = logging.Logger("main")

func main() {
	configFilePath := flag.String("config", "", "config file path")
	flag.Parse()

	config, err := config.LoadConfig(*configFilePath)
	if err != nil {
		panic(err)
	}

	if err = log.InitLogger(config); err != nil {
		panic(err)
	}

	logger.Errorf("Welcome to ai node")
	ctx := context.Background()
	nodeImpl, err := node.MakeHostNode(ctx, config)
	if err != nil {
		panic(err)
	} else {
		nodeImpl.Run(ctx)
	}

	err = node.MakeNATGossip(ctx, nodeImpl, config.NATIP, config.NATPort)
	if err != nil {
		panic(err)
	}

	// {
	// 	if chain, err := chain.MakeChainClient(ctx, config); err != nil {
	// 		panic(err)
	// 	} else {
	// 		if err = chain.Run(ctx, config.WalletKey); err != nil {
	// 			panic(err)
	// 		}
	// 	}

	// 	config.WalletKey = ""
	// }

	node.RunMDNS(nodeImpl, "Multicast DNS discovery")

	select {}
}
