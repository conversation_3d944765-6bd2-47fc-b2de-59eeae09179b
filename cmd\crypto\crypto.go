package main

import (
	"fmt"
	"math/big"

	"github.com/ethereum/go-ethereum/accounts/abi"
	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/crypto"
)

func main() {
	types := []string{"address", "uint256", "uint256", "uint256", "uint256"}
	values := []interface{}{
		common.HexToAddress("******************************************"), // 替换为实际地址
		common.HexToAddress("******************************************"), // 替换为实际地址
		big.NewInt(200),
		big.NewInt(1),
		big.NewInt(1),
		big.NewInt(2),
		big.NewInt(1),
	}

	encodedData, err := encodeData(types, values)
	if err != nil {
		fmt.Printf("Failed to encode data: %v\n", err)
		return
	}
	fmt.Printf("Encoded data: 0x%x\n", encodedData)

	prefixedMessage := fmt.Sprintf("\x19Ethereum Signed Message:\n%d%s", len(encodedData), encodedData)
	prefixedHash := crypto.Keccak256Hash([]byte(prefixedMessage))

	privateKeyHex := "7c852118294e51e653712a81e05800f419141751be58f605c371e15141b007a6" // 替换为实际私钥
	privateKey, err := crypto.HexToECDSA(privateKeyHex)
	if err != nil {
		fmt.Printf("Failed to parse private key: %v\n", err)
		return
	}

	signature, err := crypto.Sign(prefixedHash.Bytes(), privateKey)
	if err != nil {
		fmt.Printf("Failed to sign data: %v\n", err)
		return
	}

	hexString := fmt.Sprintf("%x", signature)

	fmt.Println(hexString)
}

func encodeData(types []string, values []interface{}) ([]byte, error) {
	arguments := abi.Arguments{}
	for _, typ := range types {
		argType, err := abi.NewType(typ, "", nil)
		if err != nil {
			return nil, fmt.Errorf("failed to create type %s: %v", typ, err)
		}
		arguments = append(arguments, abi.Argument{
			Type: argType,
		})
	}

	encodedData, err := arguments.Pack(values...)
	if err != nil {
		return nil, fmt.Errorf("failed to pack arguments: %v", err)
	}

	return encodedData, nil
}
