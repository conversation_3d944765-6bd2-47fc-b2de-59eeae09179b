// Code generated - DO NOT EDIT.
// This file is a generated binding and any manual changes will be lost.

package nodesgovernance

import (
	"errors"
	"math/big"
	"strings"

	ethereum "github.com/ethereum/go-ethereum"
	"github.com/ethereum/go-ethereum/accounts/abi"
	"github.com/ethereum/go-ethereum/accounts/abi/bind"
	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/core/types"
	"github.com/ethereum/go-ethereum/event"
)

// Reference imports to suppress errors if they are not otherwise used.
var (
	_ = errors.New
	_ = big.NewInt
	_ = strings.NewReader
	_ = ethereum.NotFound
	_ = bind.Bind
	_ = common.Big1
	_ = types.BloomLookup
	_ = event.NewSubscription
	_ = abi.ConvertType
)

// NodeComputeUsed is an auto generated low-level Go binding around an user-defined struct.
type NodeComputeUsed struct {
	Identifier common.Address
	GpuType    string
	Used       *big.Int
}

// NodeState is an auto generated low-level Go binding around an user-defined struct.
type NodeState struct {
	FailedCnt     uint64
	SuccessfulCnt uint64
	ExpectCnt     *big.Int
	Wallet        common.Address
	Identifier    common.Address
}

// NodesRegistryComputeAvailable is an auto generated low-level Go binding around an user-defined struct.
type NodesRegistryComputeAvailable struct {
	GpuType  string
	TotalNum *big.Int
	Used     *big.Int
}

// NodesRegistryNode is an auto generated low-level Go binding around an user-defined struct.
type NodesRegistryNode struct {
	Identifier       common.Address
	AliasIdentifier  string
	RegistrationTime *big.Int
	Active           bool
	Gpus             []NodesRegistryComputeAvailable
	Wallet           common.Address
}

// NodesGovernanceMetaData contains all meta data concerning the NodesGovernance contract.
var NodesGovernanceMetaData = &bind.MetaData{
	ABI: "[{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"spender\",\"type\":\"address\"}],\"name\":\"Authorized\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"wallet\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"address\",\"name\":\"identifier\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"time\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"string\",\"name\":\"aliasIdentifier\",\"type\":\"string\"}],\"name\":\"NodeActived\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"identifier\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"time\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"string\",\"name\":\"aliasIdentifier\",\"type\":\"string\"}],\"name\":\"NodeDeregistered\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"wallet\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"address\",\"name\":\"identifier\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"time\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"string\",\"name\":\"aliasIdentifier\",\"type\":\"string\"}],\"name\":\"NodeRegistered\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"components\":[{\"internalType\":\"uint64\",\"name\":\"failedCnt\",\"type\":\"uint64\"},{\"internalType\":\"uint64\",\"name\":\"successfulCnt\",\"type\":\"uint64\"},{\"internalType\":\"uint128\",\"name\":\"expectCnt\",\"type\":\"uint128\"},{\"internalType\":\"address\",\"name\":\"wallet\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"identifier\",\"type\":\"address\"}],\"indexed\":false,\"internalType\":\"structNodeState[]\",\"name\":\"states\",\"type\":\"tuple[]\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"totalQuota\",\"type\":\"uint256\"}],\"name\":\"SettlementResult\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"roundId\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"address\",\"name\":\"validator\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"bool\",\"name\":\"result\",\"type\":\"bool\"}],\"name\":\"ValidationResult\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"roundId\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"expectedCompletionTime\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"address\",\"name\":\"candidate\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"address[]\",\"name\":\"validators\",\"type\":\"address[]\"}],\"name\":\"ValidationStarted\",\"type\":\"event\"},{\"inputs\":[],\"name\":\"MIN_CANDIDATE\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"VALIDATOR_PER_CANDIDATE\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"startIndex\",\"type\":\"uint256\"},{\"internalType\":\"string[]\",\"name\":\"gpuTypes\",\"type\":\"string[]\"},{\"internalType\":\"uint256[]\",\"name\":\"gpuNums\",\"type\":\"uint256[]\"}],\"name\":\"allocGPU\",\"outputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"identifier\",\"type\":\"address\"},{\"internalType\":\"string\",\"name\":\"gpuType\",\"type\":\"string\"},{\"internalType\":\"uint256\",\"name\":\"used\",\"type\":\"uint256\"}],\"internalType\":\"structNodeComputeUsed[]\",\"name\":\"gpuNodes\",\"type\":\"tuple[]\"},{\"internalType\":\"uint256\",\"name\":\"len\",\"type\":\"uint256\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"allocator\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"authorizedPerson\",\"type\":\"address\"}],\"name\":\"approve\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"index\",\"type\":\"uint256\"}],\"name\":\"at\",\"outputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"identifier\",\"type\":\"address\"},{\"internalType\":\"string\",\"name\":\"aliasIdentifier\",\"type\":\"string\"},{\"internalType\":\"uint256\",\"name\":\"registrationTime\",\"type\":\"uint256\"},{\"internalType\":\"bool\",\"name\":\"active\",\"type\":\"bool\"},{\"components\":[{\"internalType\":\"string\",\"name\":\"gpuType\",\"type\":\"string\"},{\"internalType\":\"uint256\",\"name\":\"totalNum\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"used\",\"type\":\"uint256\"}],\"internalType\":\"structNodesRegistry.ComputeAvailable[]\",\"name\":\"gpus\",\"type\":\"tuple[]\"},{\"internalType\":\"address\",\"name\":\"wallet\",\"type\":\"address\"}],\"internalType\":\"structNodesRegistry.Node\",\"name\":\"node\",\"type\":\"tuple\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"name\":\"candidatePerRound\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"numOfNodes\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"expectedCompletionTime\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"identifier\",\"type\":\"address\"}],\"name\":\"check\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"currentDetectCircleId\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"currentDetectCircleStartTime\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"currentRoundId\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"currentRoundStartTime\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"deregisterNode\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"authorizer\",\"type\":\"address\"}],\"name\":\"deregisterNode\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"detectDurationTime\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"name\":\"detectPeriods\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"startId\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"endId\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"startTime\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"endTime\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"identifier\",\"type\":\"address\"},{\"internalType\":\"string\",\"name\":\"gpuType\",\"type\":\"string\"},{\"internalType\":\"uint256\",\"name\":\"used\",\"type\":\"uint256\"}],\"internalType\":\"structNodeComputeUsed[]\",\"name\":\"gpuNodes\",\"type\":\"tuple[]\"}],\"name\":\"freeGPU\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"identifier\",\"type\":\"address\"}],\"name\":\"get\",\"outputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"identifier\",\"type\":\"address\"},{\"internalType\":\"string\",\"name\":\"aliasIdentifier\",\"type\":\"string\"},{\"internalType\":\"uint256\",\"name\":\"registrationTime\",\"type\":\"uint256\"},{\"internalType\":\"bool\",\"name\":\"active\",\"type\":\"bool\"},{\"components\":[{\"internalType\":\"string\",\"name\":\"gpuType\",\"type\":\"string\"},{\"internalType\":\"uint256\",\"name\":\"totalNum\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"used\",\"type\":\"uint256\"}],\"internalType\":\"structNodesRegistry.ComputeAvailable[]\",\"name\":\"gpus\",\"type\":\"tuple[]\"},{\"internalType\":\"address\",\"name\":\"wallet\",\"type\":\"address\"}],\"internalType\":\"structNodesRegistry.Node\",\"name\":\"node\",\"type\":\"tuple\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"detectPeriodId\",\"type\":\"uint256\"}],\"name\":\"getOnePeriodSettlement\",\"outputs\":[{\"components\":[{\"internalType\":\"uint64\",\"name\":\"failedCnt\",\"type\":\"uint64\"},{\"internalType\":\"uint64\",\"name\":\"successfulCnt\",\"type\":\"uint64\"},{\"internalType\":\"uint128\",\"name\":\"expectCnt\",\"type\":\"uint128\"},{\"internalType\":\"address\",\"name\":\"wallet\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"identifier\",\"type\":\"address\"}],\"internalType\":\"structNodeState[]\",\"name\":\"states\",\"type\":\"tuple[]\"},{\"internalType\":\"uint256\",\"name\":\"totalQuotas\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"roundId\",\"type\":\"uint256\"}],\"name\":\"getRoundCandidates\",\"outputs\":[{\"internalType\":\"address[]\",\"name\":\"candidates\",\"type\":\"address[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"roundId\",\"type\":\"uint256\"},{\"internalType\":\"address\",\"name\":\"candidate\",\"type\":\"address\"}],\"name\":\"getValidatorsOfCandidate\",\"outputs\":[{\"internalType\":\"address[]\",\"name\":\"validators\",\"type\":\"address[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"name\":\"gpuSummary\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"gpuType\",\"type\":\"string\"},{\"internalType\":\"uint256\",\"name\":\"totalNum\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"used\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"},{\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"name\":\"gpuTypeOfNodes\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"length\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address[]\",\"name\":\"_identifiers\",\"type\":\"address[]\"},{\"internalType\":\"string[]\",\"name\":\"_aliasIdentifiers\",\"type\":\"string[]\"},{\"internalType\":\"address[]\",\"name\":\"_walletAccounts\",\"type\":\"address[]\"},{\"internalType\":\"string[][]\",\"name\":\"_gpuTypes\",\"type\":\"string[][]\"},{\"internalType\":\"uint256[][]\",\"name\":\"_gpuNums\",\"type\":\"uint256[][]\"},{\"internalType\":\"address\",\"name\":\"_allocator\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"_roundDurationTime\",\"type\":\"uint256\"}],\"name\":\"nodesGovernance_initialize\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"wallet\",\"type\":\"address\"},{\"internalType\":\"string\",\"name\":\"aliasIdentifier\",\"type\":\"string\"},{\"internalType\":\"string[]\",\"name\":\"gpuTypes\",\"type\":\"string[]\"},{\"internalType\":\"uint256[]\",\"name\":\"gpuNums\",\"type\":\"uint256[]\"}],\"name\":\"registerNode\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"roundDurationTime\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"detectPeriodId\",\"type\":\"uint256\"}],\"name\":\"settlementOnePeriod\",\"outputs\":[{\"components\":[{\"internalType\":\"uint64\",\"name\":\"failedCnt\",\"type\":\"uint64\"},{\"internalType\":\"uint64\",\"name\":\"successfulCnt\",\"type\":\"uint64\"},{\"internalType\":\"uint128\",\"name\":\"expectCnt\",\"type\":\"uint128\"},{\"internalType\":\"address\",\"name\":\"wallet\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"identifier\",\"type\":\"address\"}],\"internalType\":\"structNodeState[]\",\"name\":\"states\",\"type\":\"tuple[]\"},{\"internalType\":\"uint256\",\"name\":\"totalQuotas\",\"type\":\"uint256\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"startNewValidationRound\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"detectId\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"roundId\",\"type\":\"uint256\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"},{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"name\":\"validatorsPerCandidate\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"roundId\",\"type\":\"uint256\"},{\"internalType\":\"address\",\"name\":\"candidate\",\"type\":\"address\"},{\"internalType\":\"bool\",\"name\":\"result\",\"type\":\"bool\"}],\"name\":\"vote\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"},{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"name\":\"votedPerCandidate\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"candidate\",\"type\":\"address\"},{\"internalType\":\"uint128\",\"name\":\"yesVotes\",\"type\":\"uint128\"},{\"internalType\":\"uint128\",\"name\":\"noVotes\",\"type\":\"uint128\"},{\"internalType\":\"bool\",\"name\":\"completed\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"}]",
}

// NodesGovernanceABI is the input ABI used to generate the binding from.
// Deprecated: Use NodesGovernanceMetaData.ABI instead.
var NodesGovernanceABI = NodesGovernanceMetaData.ABI

// NodesGovernance is an auto generated Go binding around an Ethereum contract.
type NodesGovernance struct {
	NodesGovernanceCaller     // Read-only binding to the contract
	NodesGovernanceTransactor // Write-only binding to the contract
	NodesGovernanceFilterer   // Log filterer for contract events
}

// NodesGovernanceCaller is an auto generated read-only Go binding around an Ethereum contract.
type NodesGovernanceCaller struct {
	contract *bind.BoundContract // Generic contract wrapper for the low level calls
}

// NodesGovernanceTransactor is an auto generated write-only Go binding around an Ethereum contract.
type NodesGovernanceTransactor struct {
	contract *bind.BoundContract // Generic contract wrapper for the low level calls
}

// NodesGovernanceFilterer is an auto generated log filtering Go binding around an Ethereum contract events.
type NodesGovernanceFilterer struct {
	contract *bind.BoundContract // Generic contract wrapper for the low level calls
}

// NodesGovernanceSession is an auto generated Go binding around an Ethereum contract,
// with pre-set call and transact options.
type NodesGovernanceSession struct {
	Contract     *NodesGovernance  // Generic contract binding to set the session for
	CallOpts     bind.CallOpts     // Call options to use throughout this session
	TransactOpts bind.TransactOpts // Transaction auth options to use throughout this session
}

// NodesGovernanceCallerSession is an auto generated read-only Go binding around an Ethereum contract,
// with pre-set call options.
type NodesGovernanceCallerSession struct {
	Contract *NodesGovernanceCaller // Generic contract caller binding to set the session for
	CallOpts bind.CallOpts          // Call options to use throughout this session
}

// NodesGovernanceTransactorSession is an auto generated write-only Go binding around an Ethereum contract,
// with pre-set transact options.
type NodesGovernanceTransactorSession struct {
	Contract     *NodesGovernanceTransactor // Generic contract transactor binding to set the session for
	TransactOpts bind.TransactOpts          // Transaction auth options to use throughout this session
}

// NodesGovernanceRaw is an auto generated low-level Go binding around an Ethereum contract.
type NodesGovernanceRaw struct {
	Contract *NodesGovernance // Generic contract binding to access the raw methods on
}

// NodesGovernanceCallerRaw is an auto generated low-level read-only Go binding around an Ethereum contract.
type NodesGovernanceCallerRaw struct {
	Contract *NodesGovernanceCaller // Generic read-only contract binding to access the raw methods on
}

// NodesGovernanceTransactorRaw is an auto generated low-level write-only Go binding around an Ethereum contract.
type NodesGovernanceTransactorRaw struct {
	Contract *NodesGovernanceTransactor // Generic write-only contract binding to access the raw methods on
}

// NewNodesGovernance creates a new instance of NodesGovernance, bound to a specific deployed contract.
func NewNodesGovernance(address common.Address, backend bind.ContractBackend) (*NodesGovernance, error) {
	contract, err := bindNodesGovernance(address, backend, backend, backend)
	if err != nil {
		return nil, err
	}
	return &NodesGovernance{NodesGovernanceCaller: NodesGovernanceCaller{contract: contract}, NodesGovernanceTransactor: NodesGovernanceTransactor{contract: contract}, NodesGovernanceFilterer: NodesGovernanceFilterer{contract: contract}}, nil
}

// NewNodesGovernanceCaller creates a new read-only instance of NodesGovernance, bound to a specific deployed contract.
func NewNodesGovernanceCaller(address common.Address, caller bind.ContractCaller) (*NodesGovernanceCaller, error) {
	contract, err := bindNodesGovernance(address, caller, nil, nil)
	if err != nil {
		return nil, err
	}
	return &NodesGovernanceCaller{contract: contract}, nil
}

// NewNodesGovernanceTransactor creates a new write-only instance of NodesGovernance, bound to a specific deployed contract.
func NewNodesGovernanceTransactor(address common.Address, transactor bind.ContractTransactor) (*NodesGovernanceTransactor, error) {
	contract, err := bindNodesGovernance(address, nil, transactor, nil)
	if err != nil {
		return nil, err
	}
	return &NodesGovernanceTransactor{contract: contract}, nil
}

// NewNodesGovernanceFilterer creates a new log filterer instance of NodesGovernance, bound to a specific deployed contract.
func NewNodesGovernanceFilterer(address common.Address, filterer bind.ContractFilterer) (*NodesGovernanceFilterer, error) {
	contract, err := bindNodesGovernance(address, nil, nil, filterer)
	if err != nil {
		return nil, err
	}
	return &NodesGovernanceFilterer{contract: contract}, nil
}

// bindNodesGovernance binds a generic wrapper to an already deployed contract.
func bindNodesGovernance(address common.Address, caller bind.ContractCaller, transactor bind.ContractTransactor, filterer bind.ContractFilterer) (*bind.BoundContract, error) {
	parsed, err := NodesGovernanceMetaData.GetAbi()
	if err != nil {
		return nil, err
	}
	return bind.NewBoundContract(address, *parsed, caller, transactor, filterer), nil
}

// Call invokes the (constant) contract method with params as input values and
// sets the output to result. The result type might be a single field for simple
// returns, a slice of interfaces for anonymous returns and a struct for named
// returns.
func (_NodesGovernance *NodesGovernanceRaw) Call(opts *bind.CallOpts, result *[]interface{}, method string, params ...interface{}) error {
	return _NodesGovernance.Contract.NodesGovernanceCaller.contract.Call(opts, result, method, params...)
}

// Transfer initiates a plain transaction to move funds to the contract, calling
// its default method if one is available.
func (_NodesGovernance *NodesGovernanceRaw) Transfer(opts *bind.TransactOpts) (*types.Transaction, error) {
	return _NodesGovernance.Contract.NodesGovernanceTransactor.contract.Transfer(opts)
}

// Transact invokes the (paid) contract method with params as input values.
func (_NodesGovernance *NodesGovernanceRaw) Transact(opts *bind.TransactOpts, method string, params ...interface{}) (*types.Transaction, error) {
	return _NodesGovernance.Contract.NodesGovernanceTransactor.contract.Transact(opts, method, params...)
}

// Call invokes the (constant) contract method with params as input values and
// sets the output to result. The result type might be a single field for simple
// returns, a slice of interfaces for anonymous returns and a struct for named
// returns.
func (_NodesGovernance *NodesGovernanceCallerRaw) Call(opts *bind.CallOpts, result *[]interface{}, method string, params ...interface{}) error {
	return _NodesGovernance.Contract.contract.Call(opts, result, method, params...)
}

// Transfer initiates a plain transaction to move funds to the contract, calling
// its default method if one is available.
func (_NodesGovernance *NodesGovernanceTransactorRaw) Transfer(opts *bind.TransactOpts) (*types.Transaction, error) {
	return _NodesGovernance.Contract.contract.Transfer(opts)
}

// Transact invokes the (paid) contract method with params as input values.
func (_NodesGovernance *NodesGovernanceTransactorRaw) Transact(opts *bind.TransactOpts, method string, params ...interface{}) (*types.Transaction, error) {
	return _NodesGovernance.Contract.contract.Transact(opts, method, params...)
}

// MINCANDIDATE is a free data retrieval call binding the contract method 0x26cc3399.
//
// Solidity: function MIN_CANDIDATE() view returns(uint256)
func (_NodesGovernance *NodesGovernanceCaller) MINCANDIDATE(opts *bind.CallOpts) (*big.Int, error) {
	var out []interface{}
	err := _NodesGovernance.contract.Call(opts, &out, "MIN_CANDIDATE")

	if err != nil {
		return *new(*big.Int), err
	}

	out0 := *abi.ConvertType(out[0], new(*big.Int)).(**big.Int)

	return out0, err

}

// MINCANDIDATE is a free data retrieval call binding the contract method 0x26cc3399.
//
// Solidity: function MIN_CANDIDATE() view returns(uint256)
func (_NodesGovernance *NodesGovernanceSession) MINCANDIDATE() (*big.Int, error) {
	return _NodesGovernance.Contract.MINCANDIDATE(&_NodesGovernance.CallOpts)
}

// MINCANDIDATE is a free data retrieval call binding the contract method 0x26cc3399.
//
// Solidity: function MIN_CANDIDATE() view returns(uint256)
func (_NodesGovernance *NodesGovernanceCallerSession) MINCANDIDATE() (*big.Int, error) {
	return _NodesGovernance.Contract.MINCANDIDATE(&_NodesGovernance.CallOpts)
}

// VALIDATORPERCANDIDATE is a free data retrieval call binding the contract method 0xa7f32314.
//
// Solidity: function VALIDATOR_PER_CANDIDATE() view returns(uint256)
func (_NodesGovernance *NodesGovernanceCaller) VALIDATORPERCANDIDATE(opts *bind.CallOpts) (*big.Int, error) {
	var out []interface{}
	err := _NodesGovernance.contract.Call(opts, &out, "VALIDATOR_PER_CANDIDATE")

	if err != nil {
		return *new(*big.Int), err
	}

	out0 := *abi.ConvertType(out[0], new(*big.Int)).(**big.Int)

	return out0, err

}

// VALIDATORPERCANDIDATE is a free data retrieval call binding the contract method 0xa7f32314.
//
// Solidity: function VALIDATOR_PER_CANDIDATE() view returns(uint256)
func (_NodesGovernance *NodesGovernanceSession) VALIDATORPERCANDIDATE() (*big.Int, error) {
	return _NodesGovernance.Contract.VALIDATORPERCANDIDATE(&_NodesGovernance.CallOpts)
}

// VALIDATORPERCANDIDATE is a free data retrieval call binding the contract method 0xa7f32314.
//
// Solidity: function VALIDATOR_PER_CANDIDATE() view returns(uint256)
func (_NodesGovernance *NodesGovernanceCallerSession) VALIDATORPERCANDIDATE() (*big.Int, error) {
	return _NodesGovernance.Contract.VALIDATORPERCANDIDATE(&_NodesGovernance.CallOpts)
}

// Allocator is a free data retrieval call binding the contract method 0xaa5dcecc.
//
// Solidity: function allocator() view returns(address)
func (_NodesGovernance *NodesGovernanceCaller) Allocator(opts *bind.CallOpts) (common.Address, error) {
	var out []interface{}
	err := _NodesGovernance.contract.Call(opts, &out, "allocator")

	if err != nil {
		return *new(common.Address), err
	}

	out0 := *abi.ConvertType(out[0], new(common.Address)).(*common.Address)

	return out0, err

}

// Allocator is a free data retrieval call binding the contract method 0xaa5dcecc.
//
// Solidity: function allocator() view returns(address)
func (_NodesGovernance *NodesGovernanceSession) Allocator() (common.Address, error) {
	return _NodesGovernance.Contract.Allocator(&_NodesGovernance.CallOpts)
}

// Allocator is a free data retrieval call binding the contract method 0xaa5dcecc.
//
// Solidity: function allocator() view returns(address)
func (_NodesGovernance *NodesGovernanceCallerSession) Allocator() (common.Address, error) {
	return _NodesGovernance.Contract.Allocator(&_NodesGovernance.CallOpts)
}

// At is a free data retrieval call binding the contract method 0xe0886f90.
//
// Solidity: function at(uint256 index) view returns((address,string,uint256,bool,(string,uint256,uint256)[],address) node)
func (_NodesGovernance *NodesGovernanceCaller) At(opts *bind.CallOpts, index *big.Int) (NodesRegistryNode, error) {
	var out []interface{}
	err := _NodesGovernance.contract.Call(opts, &out, "at", index)

	if err != nil {
		return *new(NodesRegistryNode), err
	}

	out0 := *abi.ConvertType(out[0], new(NodesRegistryNode)).(*NodesRegistryNode)

	return out0, err

}

// At is a free data retrieval call binding the contract method 0xe0886f90.
//
// Solidity: function at(uint256 index) view returns((address,string,uint256,bool,(string,uint256,uint256)[],address) node)
func (_NodesGovernance *NodesGovernanceSession) At(index *big.Int) (NodesRegistryNode, error) {
	return _NodesGovernance.Contract.At(&_NodesGovernance.CallOpts, index)
}

// At is a free data retrieval call binding the contract method 0xe0886f90.
//
// Solidity: function at(uint256 index) view returns((address,string,uint256,bool,(string,uint256,uint256)[],address) node)
func (_NodesGovernance *NodesGovernanceCallerSession) At(index *big.Int) (NodesRegistryNode, error) {
	return _NodesGovernance.Contract.At(&_NodesGovernance.CallOpts, index)
}

// CandidatePerRound is a free data retrieval call binding the contract method 0x5ff5c03c.
//
// Solidity: function candidatePerRound(uint256 ) view returns(uint256 numOfNodes, uint256 expectedCompletionTime)
func (_NodesGovernance *NodesGovernanceCaller) CandidatePerRound(opts *bind.CallOpts, arg0 *big.Int) (struct {
	NumOfNodes             *big.Int
	ExpectedCompletionTime *big.Int
}, error) {
	var out []interface{}
	err := _NodesGovernance.contract.Call(opts, &out, "candidatePerRound", arg0)

	outstruct := new(struct {
		NumOfNodes             *big.Int
		ExpectedCompletionTime *big.Int
	})
	if err != nil {
		return *outstruct, err
	}

	outstruct.NumOfNodes = *abi.ConvertType(out[0], new(*big.Int)).(**big.Int)
	outstruct.ExpectedCompletionTime = *abi.ConvertType(out[1], new(*big.Int)).(**big.Int)

	return *outstruct, err

}

// CandidatePerRound is a free data retrieval call binding the contract method 0x5ff5c03c.
//
// Solidity: function candidatePerRound(uint256 ) view returns(uint256 numOfNodes, uint256 expectedCompletionTime)
func (_NodesGovernance *NodesGovernanceSession) CandidatePerRound(arg0 *big.Int) (struct {
	NumOfNodes             *big.Int
	ExpectedCompletionTime *big.Int
}, error) {
	return _NodesGovernance.Contract.CandidatePerRound(&_NodesGovernance.CallOpts, arg0)
}

// CandidatePerRound is a free data retrieval call binding the contract method 0x5ff5c03c.
//
// Solidity: function candidatePerRound(uint256 ) view returns(uint256 numOfNodes, uint256 expectedCompletionTime)
func (_NodesGovernance *NodesGovernanceCallerSession) CandidatePerRound(arg0 *big.Int) (struct {
	NumOfNodes             *big.Int
	ExpectedCompletionTime *big.Int
}, error) {
	return _NodesGovernance.Contract.CandidatePerRound(&_NodesGovernance.CallOpts, arg0)
}

// Check is a free data retrieval call binding the contract method 0xc23697a8.
//
// Solidity: function check(address identifier) view returns(bool)
func (_NodesGovernance *NodesGovernanceCaller) Check(opts *bind.CallOpts, identifier common.Address) (bool, error) {
	var out []interface{}
	err := _NodesGovernance.contract.Call(opts, &out, "check", identifier)

	if err != nil {
		return *new(bool), err
	}

	out0 := *abi.ConvertType(out[0], new(bool)).(*bool)

	return out0, err

}

// Check is a free data retrieval call binding the contract method 0xc23697a8.
//
// Solidity: function check(address identifier) view returns(bool)
func (_NodesGovernance *NodesGovernanceSession) Check(identifier common.Address) (bool, error) {
	return _NodesGovernance.Contract.Check(&_NodesGovernance.CallOpts, identifier)
}

// Check is a free data retrieval call binding the contract method 0xc23697a8.
//
// Solidity: function check(address identifier) view returns(bool)
func (_NodesGovernance *NodesGovernanceCallerSession) Check(identifier common.Address) (bool, error) {
	return _NodesGovernance.Contract.Check(&_NodesGovernance.CallOpts, identifier)
}

// CurrentDetectCircleId is a free data retrieval call binding the contract method 0x191805d8.
//
// Solidity: function currentDetectCircleId() view returns(uint256)
func (_NodesGovernance *NodesGovernanceCaller) CurrentDetectCircleId(opts *bind.CallOpts) (*big.Int, error) {
	var out []interface{}
	err := _NodesGovernance.contract.Call(opts, &out, "currentDetectCircleId")

	if err != nil {
		return *new(*big.Int), err
	}

	out0 := *abi.ConvertType(out[0], new(*big.Int)).(**big.Int)

	return out0, err

}

// CurrentDetectCircleId is a free data retrieval call binding the contract method 0x191805d8.
//
// Solidity: function currentDetectCircleId() view returns(uint256)
func (_NodesGovernance *NodesGovernanceSession) CurrentDetectCircleId() (*big.Int, error) {
	return _NodesGovernance.Contract.CurrentDetectCircleId(&_NodesGovernance.CallOpts)
}

// CurrentDetectCircleId is a free data retrieval call binding the contract method 0x191805d8.
//
// Solidity: function currentDetectCircleId() view returns(uint256)
func (_NodesGovernance *NodesGovernanceCallerSession) CurrentDetectCircleId() (*big.Int, error) {
	return _NodesGovernance.Contract.CurrentDetectCircleId(&_NodesGovernance.CallOpts)
}

// CurrentDetectCircleStartTime is a free data retrieval call binding the contract method 0x1bb12a43.
//
// Solidity: function currentDetectCircleStartTime() view returns(uint256)
func (_NodesGovernance *NodesGovernanceCaller) CurrentDetectCircleStartTime(opts *bind.CallOpts) (*big.Int, error) {
	var out []interface{}
	err := _NodesGovernance.contract.Call(opts, &out, "currentDetectCircleStartTime")

	if err != nil {
		return *new(*big.Int), err
	}

	out0 := *abi.ConvertType(out[0], new(*big.Int)).(**big.Int)

	return out0, err

}

// CurrentDetectCircleStartTime is a free data retrieval call binding the contract method 0x1bb12a43.
//
// Solidity: function currentDetectCircleStartTime() view returns(uint256)
func (_NodesGovernance *NodesGovernanceSession) CurrentDetectCircleStartTime() (*big.Int, error) {
	return _NodesGovernance.Contract.CurrentDetectCircleStartTime(&_NodesGovernance.CallOpts)
}

// CurrentDetectCircleStartTime is a free data retrieval call binding the contract method 0x1bb12a43.
//
// Solidity: function currentDetectCircleStartTime() view returns(uint256)
func (_NodesGovernance *NodesGovernanceCallerSession) CurrentDetectCircleStartTime() (*big.Int, error) {
	return _NodesGovernance.Contract.CurrentDetectCircleStartTime(&_NodesGovernance.CallOpts)
}

// CurrentRoundId is a free data retrieval call binding the contract method 0x9cbe5efd.
//
// Solidity: function currentRoundId() view returns(uint256)
func (_NodesGovernance *NodesGovernanceCaller) CurrentRoundId(opts *bind.CallOpts) (*big.Int, error) {
	var out []interface{}
	err := _NodesGovernance.contract.Call(opts, &out, "currentRoundId")

	if err != nil {
		return *new(*big.Int), err
	}

	out0 := *abi.ConvertType(out[0], new(*big.Int)).(**big.Int)

	return out0, err

}

// CurrentRoundId is a free data retrieval call binding the contract method 0x9cbe5efd.
//
// Solidity: function currentRoundId() view returns(uint256)
func (_NodesGovernance *NodesGovernanceSession) CurrentRoundId() (*big.Int, error) {
	return _NodesGovernance.Contract.CurrentRoundId(&_NodesGovernance.CallOpts)
}

// CurrentRoundId is a free data retrieval call binding the contract method 0x9cbe5efd.
//
// Solidity: function currentRoundId() view returns(uint256)
func (_NodesGovernance *NodesGovernanceCallerSession) CurrentRoundId() (*big.Int, error) {
	return _NodesGovernance.Contract.CurrentRoundId(&_NodesGovernance.CallOpts)
}

// CurrentRoundStartTime is a free data retrieval call binding the contract method 0x380dd901.
//
// Solidity: function currentRoundStartTime() view returns(uint256)
func (_NodesGovernance *NodesGovernanceCaller) CurrentRoundStartTime(opts *bind.CallOpts) (*big.Int, error) {
	var out []interface{}
	err := _NodesGovernance.contract.Call(opts, &out, "currentRoundStartTime")

	if err != nil {
		return *new(*big.Int), err
	}

	out0 := *abi.ConvertType(out[0], new(*big.Int)).(**big.Int)

	return out0, err

}

// CurrentRoundStartTime is a free data retrieval call binding the contract method 0x380dd901.
//
// Solidity: function currentRoundStartTime() view returns(uint256)
func (_NodesGovernance *NodesGovernanceSession) CurrentRoundStartTime() (*big.Int, error) {
	return _NodesGovernance.Contract.CurrentRoundStartTime(&_NodesGovernance.CallOpts)
}

// CurrentRoundStartTime is a free data retrieval call binding the contract method 0x380dd901.
//
// Solidity: function currentRoundStartTime() view returns(uint256)
func (_NodesGovernance *NodesGovernanceCallerSession) CurrentRoundStartTime() (*big.Int, error) {
	return _NodesGovernance.Contract.CurrentRoundStartTime(&_NodesGovernance.CallOpts)
}

// DetectDurationTime is a free data retrieval call binding the contract method 0x49d2b420.
//
// Solidity: function detectDurationTime() view returns(uint256)
func (_NodesGovernance *NodesGovernanceCaller) DetectDurationTime(opts *bind.CallOpts) (*big.Int, error) {
	var out []interface{}
	err := _NodesGovernance.contract.Call(opts, &out, "detectDurationTime")

	if err != nil {
		return *new(*big.Int), err
	}

	out0 := *abi.ConvertType(out[0], new(*big.Int)).(**big.Int)

	return out0, err

}

// DetectDurationTime is a free data retrieval call binding the contract method 0x49d2b420.
//
// Solidity: function detectDurationTime() view returns(uint256)
func (_NodesGovernance *NodesGovernanceSession) DetectDurationTime() (*big.Int, error) {
	return _NodesGovernance.Contract.DetectDurationTime(&_NodesGovernance.CallOpts)
}

// DetectDurationTime is a free data retrieval call binding the contract method 0x49d2b420.
//
// Solidity: function detectDurationTime() view returns(uint256)
func (_NodesGovernance *NodesGovernanceCallerSession) DetectDurationTime() (*big.Int, error) {
	return _NodesGovernance.Contract.DetectDurationTime(&_NodesGovernance.CallOpts)
}

// DetectPeriods is a free data retrieval call binding the contract method 0x698083af.
//
// Solidity: function detectPeriods(uint256 ) view returns(uint256 startId, uint256 endId, uint256 startTime, uint256 endTime)
func (_NodesGovernance *NodesGovernanceCaller) DetectPeriods(opts *bind.CallOpts, arg0 *big.Int) (struct {
	StartId   *big.Int
	EndId     *big.Int
	StartTime *big.Int
	EndTime   *big.Int
}, error) {
	var out []interface{}
	err := _NodesGovernance.contract.Call(opts, &out, "detectPeriods", arg0)

	outstruct := new(struct {
		StartId   *big.Int
		EndId     *big.Int
		StartTime *big.Int
		EndTime   *big.Int
	})
	if err != nil {
		return *outstruct, err
	}

	outstruct.StartId = *abi.ConvertType(out[0], new(*big.Int)).(**big.Int)
	outstruct.EndId = *abi.ConvertType(out[1], new(*big.Int)).(**big.Int)
	outstruct.StartTime = *abi.ConvertType(out[2], new(*big.Int)).(**big.Int)
	outstruct.EndTime = *abi.ConvertType(out[3], new(*big.Int)).(**big.Int)

	return *outstruct, err

}

// DetectPeriods is a free data retrieval call binding the contract method 0x698083af.
//
// Solidity: function detectPeriods(uint256 ) view returns(uint256 startId, uint256 endId, uint256 startTime, uint256 endTime)
func (_NodesGovernance *NodesGovernanceSession) DetectPeriods(arg0 *big.Int) (struct {
	StartId   *big.Int
	EndId     *big.Int
	StartTime *big.Int
	EndTime   *big.Int
}, error) {
	return _NodesGovernance.Contract.DetectPeriods(&_NodesGovernance.CallOpts, arg0)
}

// DetectPeriods is a free data retrieval call binding the contract method 0x698083af.
//
// Solidity: function detectPeriods(uint256 ) view returns(uint256 startId, uint256 endId, uint256 startTime, uint256 endTime)
func (_NodesGovernance *NodesGovernanceCallerSession) DetectPeriods(arg0 *big.Int) (struct {
	StartId   *big.Int
	EndId     *big.Int
	StartTime *big.Int
	EndTime   *big.Int
}, error) {
	return _NodesGovernance.Contract.DetectPeriods(&_NodesGovernance.CallOpts, arg0)
}

// Get is a free data retrieval call binding the contract method 0xc2bc2efc.
//
// Solidity: function get(address identifier) view returns((address,string,uint256,bool,(string,uint256,uint256)[],address) node)
func (_NodesGovernance *NodesGovernanceCaller) Get(opts *bind.CallOpts, identifier common.Address) (NodesRegistryNode, error) {
	var out []interface{}
	err := _NodesGovernance.contract.Call(opts, &out, "get", identifier)

	if err != nil {
		return *new(NodesRegistryNode), err
	}

	out0 := *abi.ConvertType(out[0], new(NodesRegistryNode)).(*NodesRegistryNode)

	return out0, err

}

// Get is a free data retrieval call binding the contract method 0xc2bc2efc.
//
// Solidity: function get(address identifier) view returns((address,string,uint256,bool,(string,uint256,uint256)[],address) node)
func (_NodesGovernance *NodesGovernanceSession) Get(identifier common.Address) (NodesRegistryNode, error) {
	return _NodesGovernance.Contract.Get(&_NodesGovernance.CallOpts, identifier)
}

// Get is a free data retrieval call binding the contract method 0xc2bc2efc.
//
// Solidity: function get(address identifier) view returns((address,string,uint256,bool,(string,uint256,uint256)[],address) node)
func (_NodesGovernance *NodesGovernanceCallerSession) Get(identifier common.Address) (NodesRegistryNode, error) {
	return _NodesGovernance.Contract.Get(&_NodesGovernance.CallOpts, identifier)
}

// GetOnePeriodSettlement is a free data retrieval call binding the contract method 0x6300c951.
//
// Solidity: function getOnePeriodSettlement(uint256 detectPeriodId) view returns((uint64,uint64,uint128,address,address)[] states, uint256 totalQuotas)
func (_NodesGovernance *NodesGovernanceCaller) GetOnePeriodSettlement(opts *bind.CallOpts, detectPeriodId *big.Int) (struct {
	States      []NodeState
	TotalQuotas *big.Int
}, error) {
	var out []interface{}
	err := _NodesGovernance.contract.Call(opts, &out, "getOnePeriodSettlement", detectPeriodId)

	outstruct := new(struct {
		States      []NodeState
		TotalQuotas *big.Int
	})
	if err != nil {
		return *outstruct, err
	}

	outstruct.States = *abi.ConvertType(out[0], new([]NodeState)).(*[]NodeState)
	outstruct.TotalQuotas = *abi.ConvertType(out[1], new(*big.Int)).(**big.Int)

	return *outstruct, err

}

// GetOnePeriodSettlement is a free data retrieval call binding the contract method 0x6300c951.
//
// Solidity: function getOnePeriodSettlement(uint256 detectPeriodId) view returns((uint64,uint64,uint128,address,address)[] states, uint256 totalQuotas)
func (_NodesGovernance *NodesGovernanceSession) GetOnePeriodSettlement(detectPeriodId *big.Int) (struct {
	States      []NodeState
	TotalQuotas *big.Int
}, error) {
	return _NodesGovernance.Contract.GetOnePeriodSettlement(&_NodesGovernance.CallOpts, detectPeriodId)
}

// GetOnePeriodSettlement is a free data retrieval call binding the contract method 0x6300c951.
//
// Solidity: function getOnePeriodSettlement(uint256 detectPeriodId) view returns((uint64,uint64,uint128,address,address)[] states, uint256 totalQuotas)
func (_NodesGovernance *NodesGovernanceCallerSession) GetOnePeriodSettlement(detectPeriodId *big.Int) (struct {
	States      []NodeState
	TotalQuotas *big.Int
}, error) {
	return _NodesGovernance.Contract.GetOnePeriodSettlement(&_NodesGovernance.CallOpts, detectPeriodId)
}

// GetRoundCandidates is a free data retrieval call binding the contract method 0x93e9d413.
//
// Solidity: function getRoundCandidates(uint256 roundId) view returns(address[] candidates)
func (_NodesGovernance *NodesGovernanceCaller) GetRoundCandidates(opts *bind.CallOpts, roundId *big.Int) ([]common.Address, error) {
	var out []interface{}
	err := _NodesGovernance.contract.Call(opts, &out, "getRoundCandidates", roundId)

	if err != nil {
		return *new([]common.Address), err
	}

	out0 := *abi.ConvertType(out[0], new([]common.Address)).(*[]common.Address)

	return out0, err

}

// GetRoundCandidates is a free data retrieval call binding the contract method 0x93e9d413.
//
// Solidity: function getRoundCandidates(uint256 roundId) view returns(address[] candidates)
func (_NodesGovernance *NodesGovernanceSession) GetRoundCandidates(roundId *big.Int) ([]common.Address, error) {
	return _NodesGovernance.Contract.GetRoundCandidates(&_NodesGovernance.CallOpts, roundId)
}

// GetRoundCandidates is a free data retrieval call binding the contract method 0x93e9d413.
//
// Solidity: function getRoundCandidates(uint256 roundId) view returns(address[] candidates)
func (_NodesGovernance *NodesGovernanceCallerSession) GetRoundCandidates(roundId *big.Int) ([]common.Address, error) {
	return _NodesGovernance.Contract.GetRoundCandidates(&_NodesGovernance.CallOpts, roundId)
}

// GetValidatorsOfCandidate is a free data retrieval call binding the contract method 0x0732bd7e.
//
// Solidity: function getValidatorsOfCandidate(uint256 roundId, address candidate) view returns(address[] validators)
func (_NodesGovernance *NodesGovernanceCaller) GetValidatorsOfCandidate(opts *bind.CallOpts, roundId *big.Int, candidate common.Address) ([]common.Address, error) {
	var out []interface{}
	err := _NodesGovernance.contract.Call(opts, &out, "getValidatorsOfCandidate", roundId, candidate)

	if err != nil {
		return *new([]common.Address), err
	}

	out0 := *abi.ConvertType(out[0], new([]common.Address)).(*[]common.Address)

	return out0, err

}

// GetValidatorsOfCandidate is a free data retrieval call binding the contract method 0x0732bd7e.
//
// Solidity: function getValidatorsOfCandidate(uint256 roundId, address candidate) view returns(address[] validators)
func (_NodesGovernance *NodesGovernanceSession) GetValidatorsOfCandidate(roundId *big.Int, candidate common.Address) ([]common.Address, error) {
	return _NodesGovernance.Contract.GetValidatorsOfCandidate(&_NodesGovernance.CallOpts, roundId, candidate)
}

// GetValidatorsOfCandidate is a free data retrieval call binding the contract method 0x0732bd7e.
//
// Solidity: function getValidatorsOfCandidate(uint256 roundId, address candidate) view returns(address[] validators)
func (_NodesGovernance *NodesGovernanceCallerSession) GetValidatorsOfCandidate(roundId *big.Int, candidate common.Address) ([]common.Address, error) {
	return _NodesGovernance.Contract.GetValidatorsOfCandidate(&_NodesGovernance.CallOpts, roundId, candidate)
}

// GpuSummary is a free data retrieval call binding the contract method 0xf67c5bdc.
//
// Solidity: function gpuSummary(string ) view returns(string gpuType, uint256 totalNum, uint256 used)
func (_NodesGovernance *NodesGovernanceCaller) GpuSummary(opts *bind.CallOpts, arg0 string) (struct {
	GpuType  string
	TotalNum *big.Int
	Used     *big.Int
}, error) {
	var out []interface{}
	err := _NodesGovernance.contract.Call(opts, &out, "gpuSummary", arg0)

	outstruct := new(struct {
		GpuType  string
		TotalNum *big.Int
		Used     *big.Int
	})
	if err != nil {
		return *outstruct, err
	}

	outstruct.GpuType = *abi.ConvertType(out[0], new(string)).(*string)
	outstruct.TotalNum = *abi.ConvertType(out[1], new(*big.Int)).(**big.Int)
	outstruct.Used = *abi.ConvertType(out[2], new(*big.Int)).(**big.Int)

	return *outstruct, err

}

// GpuSummary is a free data retrieval call binding the contract method 0xf67c5bdc.
//
// Solidity: function gpuSummary(string ) view returns(string gpuType, uint256 totalNum, uint256 used)
func (_NodesGovernance *NodesGovernanceSession) GpuSummary(arg0 string) (struct {
	GpuType  string
	TotalNum *big.Int
	Used     *big.Int
}, error) {
	return _NodesGovernance.Contract.GpuSummary(&_NodesGovernance.CallOpts, arg0)
}

// GpuSummary is a free data retrieval call binding the contract method 0xf67c5bdc.
//
// Solidity: function gpuSummary(string ) view returns(string gpuType, uint256 totalNum, uint256 used)
func (_NodesGovernance *NodesGovernanceCallerSession) GpuSummary(arg0 string) (struct {
	GpuType  string
	TotalNum *big.Int
	Used     *big.Int
}, error) {
	return _NodesGovernance.Contract.GpuSummary(&_NodesGovernance.CallOpts, arg0)
}

// GpuTypeOfNodes is a free data retrieval call binding the contract method 0xed38ed0d.
//
// Solidity: function gpuTypeOfNodes(address , string ) view returns(uint256)
func (_NodesGovernance *NodesGovernanceCaller) GpuTypeOfNodes(opts *bind.CallOpts, arg0 common.Address, arg1 string) (*big.Int, error) {
	var out []interface{}
	err := _NodesGovernance.contract.Call(opts, &out, "gpuTypeOfNodes", arg0, arg1)

	if err != nil {
		return *new(*big.Int), err
	}

	out0 := *abi.ConvertType(out[0], new(*big.Int)).(**big.Int)

	return out0, err

}

// GpuTypeOfNodes is a free data retrieval call binding the contract method 0xed38ed0d.
//
// Solidity: function gpuTypeOfNodes(address , string ) view returns(uint256)
func (_NodesGovernance *NodesGovernanceSession) GpuTypeOfNodes(arg0 common.Address, arg1 string) (*big.Int, error) {
	return _NodesGovernance.Contract.GpuTypeOfNodes(&_NodesGovernance.CallOpts, arg0, arg1)
}

// GpuTypeOfNodes is a free data retrieval call binding the contract method 0xed38ed0d.
//
// Solidity: function gpuTypeOfNodes(address , string ) view returns(uint256)
func (_NodesGovernance *NodesGovernanceCallerSession) GpuTypeOfNodes(arg0 common.Address, arg1 string) (*big.Int, error) {
	return _NodesGovernance.Contract.GpuTypeOfNodes(&_NodesGovernance.CallOpts, arg0, arg1)
}

// Length is a free data retrieval call binding the contract method 0x1f7b6d32.
//
// Solidity: function length() view returns(uint256)
func (_NodesGovernance *NodesGovernanceCaller) Length(opts *bind.CallOpts) (*big.Int, error) {
	var out []interface{}
	err := _NodesGovernance.contract.Call(opts, &out, "length")

	if err != nil {
		return *new(*big.Int), err
	}

	out0 := *abi.ConvertType(out[0], new(*big.Int)).(**big.Int)

	return out0, err

}

// Length is a free data retrieval call binding the contract method 0x1f7b6d32.
//
// Solidity: function length() view returns(uint256)
func (_NodesGovernance *NodesGovernanceSession) Length() (*big.Int, error) {
	return _NodesGovernance.Contract.Length(&_NodesGovernance.CallOpts)
}

// Length is a free data retrieval call binding the contract method 0x1f7b6d32.
//
// Solidity: function length() view returns(uint256)
func (_NodesGovernance *NodesGovernanceCallerSession) Length() (*big.Int, error) {
	return _NodesGovernance.Contract.Length(&_NodesGovernance.CallOpts)
}

// RoundDurationTime is a free data retrieval call binding the contract method 0xde137270.
//
// Solidity: function roundDurationTime() view returns(uint256)
func (_NodesGovernance *NodesGovernanceCaller) RoundDurationTime(opts *bind.CallOpts) (*big.Int, error) {
	var out []interface{}
	err := _NodesGovernance.contract.Call(opts, &out, "roundDurationTime")

	if err != nil {
		return *new(*big.Int), err
	}

	out0 := *abi.ConvertType(out[0], new(*big.Int)).(**big.Int)

	return out0, err

}

// RoundDurationTime is a free data retrieval call binding the contract method 0xde137270.
//
// Solidity: function roundDurationTime() view returns(uint256)
func (_NodesGovernance *NodesGovernanceSession) RoundDurationTime() (*big.Int, error) {
	return _NodesGovernance.Contract.RoundDurationTime(&_NodesGovernance.CallOpts)
}

// RoundDurationTime is a free data retrieval call binding the contract method 0xde137270.
//
// Solidity: function roundDurationTime() view returns(uint256)
func (_NodesGovernance *NodesGovernanceCallerSession) RoundDurationTime() (*big.Int, error) {
	return _NodesGovernance.Contract.RoundDurationTime(&_NodesGovernance.CallOpts)
}

// ValidatorsPerCandidate is a free data retrieval call binding the contract method 0x18767f31.
//
// Solidity: function validatorsPerCandidate(uint256 , address , uint256 ) view returns(address)
func (_NodesGovernance *NodesGovernanceCaller) ValidatorsPerCandidate(opts *bind.CallOpts, arg0 *big.Int, arg1 common.Address, arg2 *big.Int) (common.Address, error) {
	var out []interface{}
	err := _NodesGovernance.contract.Call(opts, &out, "validatorsPerCandidate", arg0, arg1, arg2)

	if err != nil {
		return *new(common.Address), err
	}

	out0 := *abi.ConvertType(out[0], new(common.Address)).(*common.Address)

	return out0, err

}

// ValidatorsPerCandidate is a free data retrieval call binding the contract method 0x18767f31.
//
// Solidity: function validatorsPerCandidate(uint256 , address , uint256 ) view returns(address)
func (_NodesGovernance *NodesGovernanceSession) ValidatorsPerCandidate(arg0 *big.Int, arg1 common.Address, arg2 *big.Int) (common.Address, error) {
	return _NodesGovernance.Contract.ValidatorsPerCandidate(&_NodesGovernance.CallOpts, arg0, arg1, arg2)
}

// ValidatorsPerCandidate is a free data retrieval call binding the contract method 0x18767f31.
//
// Solidity: function validatorsPerCandidate(uint256 , address , uint256 ) view returns(address)
func (_NodesGovernance *NodesGovernanceCallerSession) ValidatorsPerCandidate(arg0 *big.Int, arg1 common.Address, arg2 *big.Int) (common.Address, error) {
	return _NodesGovernance.Contract.ValidatorsPerCandidate(&_NodesGovernance.CallOpts, arg0, arg1, arg2)
}

// VotedPerCandidate is a free data retrieval call binding the contract method 0x2ad4f0ad.
//
// Solidity: function votedPerCandidate(uint256 , address ) view returns(address candidate, uint128 yesVotes, uint128 noVotes, bool completed)
func (_NodesGovernance *NodesGovernanceCaller) VotedPerCandidate(opts *bind.CallOpts, arg0 *big.Int, arg1 common.Address) (struct {
	Candidate common.Address
	YesVotes  *big.Int
	NoVotes   *big.Int
	Completed bool
}, error) {
	var out []interface{}
	err := _NodesGovernance.contract.Call(opts, &out, "votedPerCandidate", arg0, arg1)

	outstruct := new(struct {
		Candidate common.Address
		YesVotes  *big.Int
		NoVotes   *big.Int
		Completed bool
	})
	if err != nil {
		return *outstruct, err
	}

	outstruct.Candidate = *abi.ConvertType(out[0], new(common.Address)).(*common.Address)
	outstruct.YesVotes = *abi.ConvertType(out[1], new(*big.Int)).(**big.Int)
	outstruct.NoVotes = *abi.ConvertType(out[2], new(*big.Int)).(**big.Int)
	outstruct.Completed = *abi.ConvertType(out[3], new(bool)).(*bool)

	return *outstruct, err

}

// VotedPerCandidate is a free data retrieval call binding the contract method 0x2ad4f0ad.
//
// Solidity: function votedPerCandidate(uint256 , address ) view returns(address candidate, uint128 yesVotes, uint128 noVotes, bool completed)
func (_NodesGovernance *NodesGovernanceSession) VotedPerCandidate(arg0 *big.Int, arg1 common.Address) (struct {
	Candidate common.Address
	YesVotes  *big.Int
	NoVotes   *big.Int
	Completed bool
}, error) {
	return _NodesGovernance.Contract.VotedPerCandidate(&_NodesGovernance.CallOpts, arg0, arg1)
}

// VotedPerCandidate is a free data retrieval call binding the contract method 0x2ad4f0ad.
//
// Solidity: function votedPerCandidate(uint256 , address ) view returns(address candidate, uint128 yesVotes, uint128 noVotes, bool completed)
func (_NodesGovernance *NodesGovernanceCallerSession) VotedPerCandidate(arg0 *big.Int, arg1 common.Address) (struct {
	Candidate common.Address
	YesVotes  *big.Int
	NoVotes   *big.Int
	Completed bool
}, error) {
	return _NodesGovernance.Contract.VotedPerCandidate(&_NodesGovernance.CallOpts, arg0, arg1)
}

// AllocGPU is a paid mutator transaction binding the contract method 0x6252e1c2.
//
// Solidity: function allocGPU(uint256 startIndex, string[] gpuTypes, uint256[] gpuNums) returns((address,string,uint256)[] gpuNodes, uint256 len)
func (_NodesGovernance *NodesGovernanceTransactor) AllocGPU(opts *bind.TransactOpts, startIndex *big.Int, gpuTypes []string, gpuNums []*big.Int) (*types.Transaction, error) {
	return _NodesGovernance.contract.Transact(opts, "allocGPU", startIndex, gpuTypes, gpuNums)
}

// AllocGPU is a paid mutator transaction binding the contract method 0x6252e1c2.
//
// Solidity: function allocGPU(uint256 startIndex, string[] gpuTypes, uint256[] gpuNums) returns((address,string,uint256)[] gpuNodes, uint256 len)
func (_NodesGovernance *NodesGovernanceSession) AllocGPU(startIndex *big.Int, gpuTypes []string, gpuNums []*big.Int) (*types.Transaction, error) {
	return _NodesGovernance.Contract.AllocGPU(&_NodesGovernance.TransactOpts, startIndex, gpuTypes, gpuNums)
}

// AllocGPU is a paid mutator transaction binding the contract method 0x6252e1c2.
//
// Solidity: function allocGPU(uint256 startIndex, string[] gpuTypes, uint256[] gpuNums) returns((address,string,uint256)[] gpuNodes, uint256 len)
func (_NodesGovernance *NodesGovernanceTransactorSession) AllocGPU(startIndex *big.Int, gpuTypes []string, gpuNums []*big.Int) (*types.Transaction, error) {
	return _NodesGovernance.Contract.AllocGPU(&_NodesGovernance.TransactOpts, startIndex, gpuTypes, gpuNums)
}

// Approve is a paid mutator transaction binding the contract method 0xdaea85c5.
//
// Solidity: function approve(address authorizedPerson) returns(bool)
func (_NodesGovernance *NodesGovernanceTransactor) Approve(opts *bind.TransactOpts, authorizedPerson common.Address) (*types.Transaction, error) {
	return _NodesGovernance.contract.Transact(opts, "approve", authorizedPerson)
}

// Approve is a paid mutator transaction binding the contract method 0xdaea85c5.
//
// Solidity: function approve(address authorizedPerson) returns(bool)
func (_NodesGovernance *NodesGovernanceSession) Approve(authorizedPerson common.Address) (*types.Transaction, error) {
	return _NodesGovernance.Contract.Approve(&_NodesGovernance.TransactOpts, authorizedPerson)
}

// Approve is a paid mutator transaction binding the contract method 0xdaea85c5.
//
// Solidity: function approve(address authorizedPerson) returns(bool)
func (_NodesGovernance *NodesGovernanceTransactorSession) Approve(authorizedPerson common.Address) (*types.Transaction, error) {
	return _NodesGovernance.Contract.Approve(&_NodesGovernance.TransactOpts, authorizedPerson)
}

// DeregisterNode is a paid mutator transaction binding the contract method 0x18b1c081.
//
// Solidity: function deregisterNode() returns()
func (_NodesGovernance *NodesGovernanceTransactor) DeregisterNode(opts *bind.TransactOpts) (*types.Transaction, error) {
	return _NodesGovernance.contract.Transact(opts, "deregisterNode")
}

// DeregisterNode is a paid mutator transaction binding the contract method 0x18b1c081.
//
// Solidity: function deregisterNode() returns()
func (_NodesGovernance *NodesGovernanceSession) DeregisterNode() (*types.Transaction, error) {
	return _NodesGovernance.Contract.DeregisterNode(&_NodesGovernance.TransactOpts)
}

// DeregisterNode is a paid mutator transaction binding the contract method 0x18b1c081.
//
// Solidity: function deregisterNode() returns()
func (_NodesGovernance *NodesGovernanceTransactorSession) DeregisterNode() (*types.Transaction, error) {
	return _NodesGovernance.Contract.DeregisterNode(&_NodesGovernance.TransactOpts)
}

// DeregisterNode0 is a paid mutator transaction binding the contract method 0xe7658aaf.
//
// Solidity: function deregisterNode(address authorizer) returns()
func (_NodesGovernance *NodesGovernanceTransactor) DeregisterNode0(opts *bind.TransactOpts, authorizer common.Address) (*types.Transaction, error) {
	return _NodesGovernance.contract.Transact(opts, "deregisterNode0", authorizer)
}

// DeregisterNode0 is a paid mutator transaction binding the contract method 0xe7658aaf.
//
// Solidity: function deregisterNode(address authorizer) returns()
func (_NodesGovernance *NodesGovernanceSession) DeregisterNode0(authorizer common.Address) (*types.Transaction, error) {
	return _NodesGovernance.Contract.DeregisterNode0(&_NodesGovernance.TransactOpts, authorizer)
}

// DeregisterNode0 is a paid mutator transaction binding the contract method 0xe7658aaf.
//
// Solidity: function deregisterNode(address authorizer) returns()
func (_NodesGovernance *NodesGovernanceTransactorSession) DeregisterNode0(authorizer common.Address) (*types.Transaction, error) {
	return _NodesGovernance.Contract.DeregisterNode0(&_NodesGovernance.TransactOpts, authorizer)
}

// FreeGPU is a paid mutator transaction binding the contract method 0x036fe9c2.
//
// Solidity: function freeGPU((address,string,uint256)[] gpuNodes) returns()
func (_NodesGovernance *NodesGovernanceTransactor) FreeGPU(opts *bind.TransactOpts, gpuNodes []NodeComputeUsed) (*types.Transaction, error) {
	return _NodesGovernance.contract.Transact(opts, "freeGPU", gpuNodes)
}

// FreeGPU is a paid mutator transaction binding the contract method 0x036fe9c2.
//
// Solidity: function freeGPU((address,string,uint256)[] gpuNodes) returns()
func (_NodesGovernance *NodesGovernanceSession) FreeGPU(gpuNodes []NodeComputeUsed) (*types.Transaction, error) {
	return _NodesGovernance.Contract.FreeGPU(&_NodesGovernance.TransactOpts, gpuNodes)
}

// FreeGPU is a paid mutator transaction binding the contract method 0x036fe9c2.
//
// Solidity: function freeGPU((address,string,uint256)[] gpuNodes) returns()
func (_NodesGovernance *NodesGovernanceTransactorSession) FreeGPU(gpuNodes []NodeComputeUsed) (*types.Transaction, error) {
	return _NodesGovernance.Contract.FreeGPU(&_NodesGovernance.TransactOpts, gpuNodes)
}

// NodesGovernanceInitialize is a paid mutator transaction binding the contract method 0x32c56a67.
//
// Solidity: function nodesGovernance_initialize(address[] _identifiers, string[] _aliasIdentifiers, address[] _walletAccounts, string[][] _gpuTypes, uint256[][] _gpuNums, address _allocator, uint256 _roundDurationTime) returns()
func (_NodesGovernance *NodesGovernanceTransactor) NodesGovernanceInitialize(opts *bind.TransactOpts, _identifiers []common.Address, _aliasIdentifiers []string, _walletAccounts []common.Address, _gpuTypes [][]string, _gpuNums [][]*big.Int, _allocator common.Address, _roundDurationTime *big.Int) (*types.Transaction, error) {
	return _NodesGovernance.contract.Transact(opts, "nodesGovernance_initialize", _identifiers, _aliasIdentifiers, _walletAccounts, _gpuTypes, _gpuNums, _allocator, _roundDurationTime)
}

// NodesGovernanceInitialize is a paid mutator transaction binding the contract method 0x32c56a67.
//
// Solidity: function nodesGovernance_initialize(address[] _identifiers, string[] _aliasIdentifiers, address[] _walletAccounts, string[][] _gpuTypes, uint256[][] _gpuNums, address _allocator, uint256 _roundDurationTime) returns()
func (_NodesGovernance *NodesGovernanceSession) NodesGovernanceInitialize(_identifiers []common.Address, _aliasIdentifiers []string, _walletAccounts []common.Address, _gpuTypes [][]string, _gpuNums [][]*big.Int, _allocator common.Address, _roundDurationTime *big.Int) (*types.Transaction, error) {
	return _NodesGovernance.Contract.NodesGovernanceInitialize(&_NodesGovernance.TransactOpts, _identifiers, _aliasIdentifiers, _walletAccounts, _gpuTypes, _gpuNums, _allocator, _roundDurationTime)
}

// NodesGovernanceInitialize is a paid mutator transaction binding the contract method 0x32c56a67.
//
// Solidity: function nodesGovernance_initialize(address[] _identifiers, string[] _aliasIdentifiers, address[] _walletAccounts, string[][] _gpuTypes, uint256[][] _gpuNums, address _allocator, uint256 _roundDurationTime) returns()
func (_NodesGovernance *NodesGovernanceTransactorSession) NodesGovernanceInitialize(_identifiers []common.Address, _aliasIdentifiers []string, _walletAccounts []common.Address, _gpuTypes [][]string, _gpuNums [][]*big.Int, _allocator common.Address, _roundDurationTime *big.Int) (*types.Transaction, error) {
	return _NodesGovernance.Contract.NodesGovernanceInitialize(&_NodesGovernance.TransactOpts, _identifiers, _aliasIdentifiers, _walletAccounts, _gpuTypes, _gpuNums, _allocator, _roundDurationTime)
}

// RegisterNode is a paid mutator transaction binding the contract method 0xefca74d2.
//
// Solidity: function registerNode(address wallet, string aliasIdentifier, string[] gpuTypes, uint256[] gpuNums) returns()
func (_NodesGovernance *NodesGovernanceTransactor) RegisterNode(opts *bind.TransactOpts, wallet common.Address, aliasIdentifier string, gpuTypes []string, gpuNums []*big.Int) (*types.Transaction, error) {
	return _NodesGovernance.contract.Transact(opts, "registerNode", wallet, aliasIdentifier, gpuTypes, gpuNums)
}

// RegisterNode is a paid mutator transaction binding the contract method 0xefca74d2.
//
// Solidity: function registerNode(address wallet, string aliasIdentifier, string[] gpuTypes, uint256[] gpuNums) returns()
func (_NodesGovernance *NodesGovernanceSession) RegisterNode(wallet common.Address, aliasIdentifier string, gpuTypes []string, gpuNums []*big.Int) (*types.Transaction, error) {
	return _NodesGovernance.Contract.RegisterNode(&_NodesGovernance.TransactOpts, wallet, aliasIdentifier, gpuTypes, gpuNums)
}

// RegisterNode is a paid mutator transaction binding the contract method 0xefca74d2.
//
// Solidity: function registerNode(address wallet, string aliasIdentifier, string[] gpuTypes, uint256[] gpuNums) returns()
func (_NodesGovernance *NodesGovernanceTransactorSession) RegisterNode(wallet common.Address, aliasIdentifier string, gpuTypes []string, gpuNums []*big.Int) (*types.Transaction, error) {
	return _NodesGovernance.Contract.RegisterNode(&_NodesGovernance.TransactOpts, wallet, aliasIdentifier, gpuTypes, gpuNums)
}

// SettlementOnePeriod is a paid mutator transaction binding the contract method 0x4d4fc0b8.
//
// Solidity: function settlementOnePeriod(uint256 detectPeriodId) returns((uint64,uint64,uint128,address,address)[] states, uint256 totalQuotas)
func (_NodesGovernance *NodesGovernanceTransactor) SettlementOnePeriod(opts *bind.TransactOpts, detectPeriodId *big.Int) (*types.Transaction, error) {
	return _NodesGovernance.contract.Transact(opts, "settlementOnePeriod", detectPeriodId)
}

// SettlementOnePeriod is a paid mutator transaction binding the contract method 0x4d4fc0b8.
//
// Solidity: function settlementOnePeriod(uint256 detectPeriodId) returns((uint64,uint64,uint128,address,address)[] states, uint256 totalQuotas)
func (_NodesGovernance *NodesGovernanceSession) SettlementOnePeriod(detectPeriodId *big.Int) (*types.Transaction, error) {
	return _NodesGovernance.Contract.SettlementOnePeriod(&_NodesGovernance.TransactOpts, detectPeriodId)
}

// SettlementOnePeriod is a paid mutator transaction binding the contract method 0x4d4fc0b8.
//
// Solidity: function settlementOnePeriod(uint256 detectPeriodId) returns((uint64,uint64,uint128,address,address)[] states, uint256 totalQuotas)
func (_NodesGovernance *NodesGovernanceTransactorSession) SettlementOnePeriod(detectPeriodId *big.Int) (*types.Transaction, error) {
	return _NodesGovernance.Contract.SettlementOnePeriod(&_NodesGovernance.TransactOpts, detectPeriodId)
}

// StartNewValidationRound is a paid mutator transaction binding the contract method 0x63c94199.
//
// Solidity: function startNewValidationRound() returns(uint256 detectId, uint256 roundId)
func (_NodesGovernance *NodesGovernanceTransactor) StartNewValidationRound(opts *bind.TransactOpts) (*types.Transaction, error) {
	return _NodesGovernance.contract.Transact(opts, "startNewValidationRound")
}

// StartNewValidationRound is a paid mutator transaction binding the contract method 0x63c94199.
//
// Solidity: function startNewValidationRound() returns(uint256 detectId, uint256 roundId)
func (_NodesGovernance *NodesGovernanceSession) StartNewValidationRound() (*types.Transaction, error) {
	return _NodesGovernance.Contract.StartNewValidationRound(&_NodesGovernance.TransactOpts)
}

// StartNewValidationRound is a paid mutator transaction binding the contract method 0x63c94199.
//
// Solidity: function startNewValidationRound() returns(uint256 detectId, uint256 roundId)
func (_NodesGovernance *NodesGovernanceTransactorSession) StartNewValidationRound() (*types.Transaction, error) {
	return _NodesGovernance.Contract.StartNewValidationRound(&_NodesGovernance.TransactOpts)
}

// Vote is a paid mutator transaction binding the contract method 0xe2cdd42a.
//
// Solidity: function vote(uint256 roundId, address candidate, bool result) returns()
func (_NodesGovernance *NodesGovernanceTransactor) Vote(opts *bind.TransactOpts, roundId *big.Int, candidate common.Address, result bool) (*types.Transaction, error) {
	return _NodesGovernance.contract.Transact(opts, "vote", roundId, candidate, result)
}

// Vote is a paid mutator transaction binding the contract method 0xe2cdd42a.
//
// Solidity: function vote(uint256 roundId, address candidate, bool result) returns()
func (_NodesGovernance *NodesGovernanceSession) Vote(roundId *big.Int, candidate common.Address, result bool) (*types.Transaction, error) {
	return _NodesGovernance.Contract.Vote(&_NodesGovernance.TransactOpts, roundId, candidate, result)
}

// Vote is a paid mutator transaction binding the contract method 0xe2cdd42a.
//
// Solidity: function vote(uint256 roundId, address candidate, bool result) returns()
func (_NodesGovernance *NodesGovernanceTransactorSession) Vote(roundId *big.Int, candidate common.Address, result bool) (*types.Transaction, error) {
	return _NodesGovernance.Contract.Vote(&_NodesGovernance.TransactOpts, roundId, candidate, result)
}

// NodesGovernanceAuthorizedIterator is returned from FilterAuthorized and is used to iterate over the raw logs and unpacked data for Authorized events raised by the NodesGovernance contract.
type NodesGovernanceAuthorizedIterator struct {
	Event *NodesGovernanceAuthorized // Event containing the contract specifics and raw log

	contract *bind.BoundContract // Generic contract to use for unpacking event data
	event    string              // Event name to use for unpacking event data

	logs chan types.Log        // Log channel receiving the found contract events
	sub  ethereum.Subscription // Subscription for errors, completion and termination
	done bool                  // Whether the subscription completed delivering logs
	fail error                 // Occurred error to stop iteration
}

// Next advances the iterator to the subsequent event, returning whether there
// are any more events found. In case of a retrieval or parsing error, false is
// returned and Error() can be queried for the exact failure.
func (it *NodesGovernanceAuthorizedIterator) Next() bool {
	// If the iterator failed, stop iterating
	if it.fail != nil {
		return false
	}
	// If the iterator completed, deliver directly whatever's available
	if it.done {
		select {
		case log := <-it.logs:
			it.Event = new(NodesGovernanceAuthorized)
			if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
				it.fail = err
				return false
			}
			it.Event.Raw = log
			return true

		default:
			return false
		}
	}
	// Iterator still in progress, wait for either a data or an error event
	select {
	case log := <-it.logs:
		it.Event = new(NodesGovernanceAuthorized)
		if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
			it.fail = err
			return false
		}
		it.Event.Raw = log
		return true

	case err := <-it.sub.Err():
		it.done = true
		it.fail = err
		return it.Next()
	}
}

// Error returns any retrieval or parsing error occurred during filtering.
func (it *NodesGovernanceAuthorizedIterator) Error() error {
	return it.fail
}

// Close terminates the iteration process, releasing any pending underlying
// resources.
func (it *NodesGovernanceAuthorizedIterator) Close() error {
	it.sub.Unsubscribe()
	return nil
}

// NodesGovernanceAuthorized represents a Authorized event raised by the NodesGovernance contract.
type NodesGovernanceAuthorized struct {
	Owner   common.Address
	Spender common.Address
	Raw     types.Log // Blockchain specific contextual infos
}

// FilterAuthorized is a free log retrieval operation binding the contract event 0xf5a7f4fb8a92356e8c8c4ae7ac3589908381450500a7e2fd08c95600021ee889.
//
// Solidity: event Authorized(address indexed owner, address indexed spender)
func (_NodesGovernance *NodesGovernanceFilterer) FilterAuthorized(opts *bind.FilterOpts, owner []common.Address, spender []common.Address) (*NodesGovernanceAuthorizedIterator, error) {

	var ownerRule []interface{}
	for _, ownerItem := range owner {
		ownerRule = append(ownerRule, ownerItem)
	}
	var spenderRule []interface{}
	for _, spenderItem := range spender {
		spenderRule = append(spenderRule, spenderItem)
	}

	logs, sub, err := _NodesGovernance.contract.FilterLogs(opts, "Authorized", ownerRule, spenderRule)
	if err != nil {
		return nil, err
	}
	return &NodesGovernanceAuthorizedIterator{contract: _NodesGovernance.contract, event: "Authorized", logs: logs, sub: sub}, nil
}

// WatchAuthorized is a free log subscription operation binding the contract event 0xf5a7f4fb8a92356e8c8c4ae7ac3589908381450500a7e2fd08c95600021ee889.
//
// Solidity: event Authorized(address indexed owner, address indexed spender)
func (_NodesGovernance *NodesGovernanceFilterer) WatchAuthorized(opts *bind.WatchOpts, sink chan<- *NodesGovernanceAuthorized, owner []common.Address, spender []common.Address) (event.Subscription, error) {

	var ownerRule []interface{}
	for _, ownerItem := range owner {
		ownerRule = append(ownerRule, ownerItem)
	}
	var spenderRule []interface{}
	for _, spenderItem := range spender {
		spenderRule = append(spenderRule, spenderItem)
	}

	logs, sub, err := _NodesGovernance.contract.WatchLogs(opts, "Authorized", ownerRule, spenderRule)
	if err != nil {
		return nil, err
	}
	return event.NewSubscription(func(quit <-chan struct{}) error {
		defer sub.Unsubscribe()
		for {
			select {
			case log := <-logs:
				// New log arrived, parse the event and forward to the user
				event := new(NodesGovernanceAuthorized)
				if err := _NodesGovernance.contract.UnpackLog(event, "Authorized", log); err != nil {
					return err
				}
				event.Raw = log

				select {
				case sink <- event:
				case err := <-sub.Err():
					return err
				case <-quit:
					return nil
				}
			case err := <-sub.Err():
				return err
			case <-quit:
				return nil
			}
		}
	}), nil
}

// ParseAuthorized is a log parse operation binding the contract event 0xf5a7f4fb8a92356e8c8c4ae7ac3589908381450500a7e2fd08c95600021ee889.
//
// Solidity: event Authorized(address indexed owner, address indexed spender)
func (_NodesGovernance *NodesGovernanceFilterer) ParseAuthorized(log types.Log) (*NodesGovernanceAuthorized, error) {
	event := new(NodesGovernanceAuthorized)
	if err := _NodesGovernance.contract.UnpackLog(event, "Authorized", log); err != nil {
		return nil, err
	}
	event.Raw = log
	return event, nil
}

// NodesGovernanceNodeActivedIterator is returned from FilterNodeActived and is used to iterate over the raw logs and unpacked data for NodeActived events raised by the NodesGovernance contract.
type NodesGovernanceNodeActivedIterator struct {
	Event *NodesGovernanceNodeActived // Event containing the contract specifics and raw log

	contract *bind.BoundContract // Generic contract to use for unpacking event data
	event    string              // Event name to use for unpacking event data

	logs chan types.Log        // Log channel receiving the found contract events
	sub  ethereum.Subscription // Subscription for errors, completion and termination
	done bool                  // Whether the subscription completed delivering logs
	fail error                 // Occurred error to stop iteration
}

// Next advances the iterator to the subsequent event, returning whether there
// are any more events found. In case of a retrieval or parsing error, false is
// returned and Error() can be queried for the exact failure.
func (it *NodesGovernanceNodeActivedIterator) Next() bool {
	// If the iterator failed, stop iterating
	if it.fail != nil {
		return false
	}
	// If the iterator completed, deliver directly whatever's available
	if it.done {
		select {
		case log := <-it.logs:
			it.Event = new(NodesGovernanceNodeActived)
			if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
				it.fail = err
				return false
			}
			it.Event.Raw = log
			return true

		default:
			return false
		}
	}
	// Iterator still in progress, wait for either a data or an error event
	select {
	case log := <-it.logs:
		it.Event = new(NodesGovernanceNodeActived)
		if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
			it.fail = err
			return false
		}
		it.Event.Raw = log
		return true

	case err := <-it.sub.Err():
		it.done = true
		it.fail = err
		return it.Next()
	}
}

// Error returns any retrieval or parsing error occurred during filtering.
func (it *NodesGovernanceNodeActivedIterator) Error() error {
	return it.fail
}

// Close terminates the iteration process, releasing any pending underlying
// resources.
func (it *NodesGovernanceNodeActivedIterator) Close() error {
	it.sub.Unsubscribe()
	return nil
}

// NodesGovernanceNodeActived represents a NodeActived event raised by the NodesGovernance contract.
type NodesGovernanceNodeActived struct {
	Wallet          common.Address
	Identifier      common.Address
	Time            *big.Int
	AliasIdentifier string
	Raw             types.Log // Blockchain specific contextual infos
}

// FilterNodeActived is a free log retrieval operation binding the contract event 0x25cdb56344c72b35eff48499be35553cea1cf3ee21c613692d20ea5a5c539e37.
//
// Solidity: event NodeActived(address indexed wallet, address identifier, uint256 time, string aliasIdentifier)
func (_NodesGovernance *NodesGovernanceFilterer) FilterNodeActived(opts *bind.FilterOpts, wallet []common.Address) (*NodesGovernanceNodeActivedIterator, error) {

	var walletRule []interface{}
	for _, walletItem := range wallet {
		walletRule = append(walletRule, walletItem)
	}

	logs, sub, err := _NodesGovernance.contract.FilterLogs(opts, "NodeActived", walletRule)
	if err != nil {
		return nil, err
	}
	return &NodesGovernanceNodeActivedIterator{contract: _NodesGovernance.contract, event: "NodeActived", logs: logs, sub: sub}, nil
}

// WatchNodeActived is a free log subscription operation binding the contract event 0x25cdb56344c72b35eff48499be35553cea1cf3ee21c613692d20ea5a5c539e37.
//
// Solidity: event NodeActived(address indexed wallet, address identifier, uint256 time, string aliasIdentifier)
func (_NodesGovernance *NodesGovernanceFilterer) WatchNodeActived(opts *bind.WatchOpts, sink chan<- *NodesGovernanceNodeActived, wallet []common.Address) (event.Subscription, error) {

	var walletRule []interface{}
	for _, walletItem := range wallet {
		walletRule = append(walletRule, walletItem)
	}

	logs, sub, err := _NodesGovernance.contract.WatchLogs(opts, "NodeActived", walletRule)
	if err != nil {
		return nil, err
	}
	return event.NewSubscription(func(quit <-chan struct{}) error {
		defer sub.Unsubscribe()
		for {
			select {
			case log := <-logs:
				// New log arrived, parse the event and forward to the user
				event := new(NodesGovernanceNodeActived)
				if err := _NodesGovernance.contract.UnpackLog(event, "NodeActived", log); err != nil {
					return err
				}
				event.Raw = log

				select {
				case sink <- event:
				case err := <-sub.Err():
					return err
				case <-quit:
					return nil
				}
			case err := <-sub.Err():
				return err
			case <-quit:
				return nil
			}
		}
	}), nil
}

// ParseNodeActived is a log parse operation binding the contract event 0x25cdb56344c72b35eff48499be35553cea1cf3ee21c613692d20ea5a5c539e37.
//
// Solidity: event NodeActived(address indexed wallet, address identifier, uint256 time, string aliasIdentifier)
func (_NodesGovernance *NodesGovernanceFilterer) ParseNodeActived(log types.Log) (*NodesGovernanceNodeActived, error) {
	event := new(NodesGovernanceNodeActived)
	if err := _NodesGovernance.contract.UnpackLog(event, "NodeActived", log); err != nil {
		return nil, err
	}
	event.Raw = log
	return event, nil
}

// NodesGovernanceNodeDeregisteredIterator is returned from FilterNodeDeregistered and is used to iterate over the raw logs and unpacked data for NodeDeregistered events raised by the NodesGovernance contract.
type NodesGovernanceNodeDeregisteredIterator struct {
	Event *NodesGovernanceNodeDeregistered // Event containing the contract specifics and raw log

	contract *bind.BoundContract // Generic contract to use for unpacking event data
	event    string              // Event name to use for unpacking event data

	logs chan types.Log        // Log channel receiving the found contract events
	sub  ethereum.Subscription // Subscription for errors, completion and termination
	done bool                  // Whether the subscription completed delivering logs
	fail error                 // Occurred error to stop iteration
}

// Next advances the iterator to the subsequent event, returning whether there
// are any more events found. In case of a retrieval or parsing error, false is
// returned and Error() can be queried for the exact failure.
func (it *NodesGovernanceNodeDeregisteredIterator) Next() bool {
	// If the iterator failed, stop iterating
	if it.fail != nil {
		return false
	}
	// If the iterator completed, deliver directly whatever's available
	if it.done {
		select {
		case log := <-it.logs:
			it.Event = new(NodesGovernanceNodeDeregistered)
			if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
				it.fail = err
				return false
			}
			it.Event.Raw = log
			return true

		default:
			return false
		}
	}
	// Iterator still in progress, wait for either a data or an error event
	select {
	case log := <-it.logs:
		it.Event = new(NodesGovernanceNodeDeregistered)
		if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
			it.fail = err
			return false
		}
		it.Event.Raw = log
		return true

	case err := <-it.sub.Err():
		it.done = true
		it.fail = err
		return it.Next()
	}
}

// Error returns any retrieval or parsing error occurred during filtering.
func (it *NodesGovernanceNodeDeregisteredIterator) Error() error {
	return it.fail
}

// Close terminates the iteration process, releasing any pending underlying
// resources.
func (it *NodesGovernanceNodeDeregisteredIterator) Close() error {
	it.sub.Unsubscribe()
	return nil
}

// NodesGovernanceNodeDeregistered represents a NodeDeregistered event raised by the NodesGovernance contract.
type NodesGovernanceNodeDeregistered struct {
	Identifier      common.Address
	Time            *big.Int
	AliasIdentifier string
	Raw             types.Log // Blockchain specific contextual infos
}

// FilterNodeDeregistered is a free log retrieval operation binding the contract event 0x60d01d146c7aa1a7d4e3fdd5543872f7d5b2a241980a66b3552ae1a86ae18453.
//
// Solidity: event NodeDeregistered(address indexed identifier, uint256 time, string aliasIdentifier)
func (_NodesGovernance *NodesGovernanceFilterer) FilterNodeDeregistered(opts *bind.FilterOpts, identifier []common.Address) (*NodesGovernanceNodeDeregisteredIterator, error) {

	var identifierRule []interface{}
	for _, identifierItem := range identifier {
		identifierRule = append(identifierRule, identifierItem)
	}

	logs, sub, err := _NodesGovernance.contract.FilterLogs(opts, "NodeDeregistered", identifierRule)
	if err != nil {
		return nil, err
	}
	return &NodesGovernanceNodeDeregisteredIterator{contract: _NodesGovernance.contract, event: "NodeDeregistered", logs: logs, sub: sub}, nil
}

// WatchNodeDeregistered is a free log subscription operation binding the contract event 0x60d01d146c7aa1a7d4e3fdd5543872f7d5b2a241980a66b3552ae1a86ae18453.
//
// Solidity: event NodeDeregistered(address indexed identifier, uint256 time, string aliasIdentifier)
func (_NodesGovernance *NodesGovernanceFilterer) WatchNodeDeregistered(opts *bind.WatchOpts, sink chan<- *NodesGovernanceNodeDeregistered, identifier []common.Address) (event.Subscription, error) {

	var identifierRule []interface{}
	for _, identifierItem := range identifier {
		identifierRule = append(identifierRule, identifierItem)
	}

	logs, sub, err := _NodesGovernance.contract.WatchLogs(opts, "NodeDeregistered", identifierRule)
	if err != nil {
		return nil, err
	}
	return event.NewSubscription(func(quit <-chan struct{}) error {
		defer sub.Unsubscribe()
		for {
			select {
			case log := <-logs:
				// New log arrived, parse the event and forward to the user
				event := new(NodesGovernanceNodeDeregistered)
				if err := _NodesGovernance.contract.UnpackLog(event, "NodeDeregistered", log); err != nil {
					return err
				}
				event.Raw = log

				select {
				case sink <- event:
				case err := <-sub.Err():
					return err
				case <-quit:
					return nil
				}
			case err := <-sub.Err():
				return err
			case <-quit:
				return nil
			}
		}
	}), nil
}

// ParseNodeDeregistered is a log parse operation binding the contract event 0x60d01d146c7aa1a7d4e3fdd5543872f7d5b2a241980a66b3552ae1a86ae18453.
//
// Solidity: event NodeDeregistered(address indexed identifier, uint256 time, string aliasIdentifier)
func (_NodesGovernance *NodesGovernanceFilterer) ParseNodeDeregistered(log types.Log) (*NodesGovernanceNodeDeregistered, error) {
	event := new(NodesGovernanceNodeDeregistered)
	if err := _NodesGovernance.contract.UnpackLog(event, "NodeDeregistered", log); err != nil {
		return nil, err
	}
	event.Raw = log
	return event, nil
}

// NodesGovernanceNodeRegisteredIterator is returned from FilterNodeRegistered and is used to iterate over the raw logs and unpacked data for NodeRegistered events raised by the NodesGovernance contract.
type NodesGovernanceNodeRegisteredIterator struct {
	Event *NodesGovernanceNodeRegistered // Event containing the contract specifics and raw log

	contract *bind.BoundContract // Generic contract to use for unpacking event data
	event    string              // Event name to use for unpacking event data

	logs chan types.Log        // Log channel receiving the found contract events
	sub  ethereum.Subscription // Subscription for errors, completion and termination
	done bool                  // Whether the subscription completed delivering logs
	fail error                 // Occurred error to stop iteration
}

// Next advances the iterator to the subsequent event, returning whether there
// are any more events found. In case of a retrieval or parsing error, false is
// returned and Error() can be queried for the exact failure.
func (it *NodesGovernanceNodeRegisteredIterator) Next() bool {
	// If the iterator failed, stop iterating
	if it.fail != nil {
		return false
	}
	// If the iterator completed, deliver directly whatever's available
	if it.done {
		select {
		case log := <-it.logs:
			it.Event = new(NodesGovernanceNodeRegistered)
			if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
				it.fail = err
				return false
			}
			it.Event.Raw = log
			return true

		default:
			return false
		}
	}
	// Iterator still in progress, wait for either a data or an error event
	select {
	case log := <-it.logs:
		it.Event = new(NodesGovernanceNodeRegistered)
		if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
			it.fail = err
			return false
		}
		it.Event.Raw = log
		return true

	case err := <-it.sub.Err():
		it.done = true
		it.fail = err
		return it.Next()
	}
}

// Error returns any retrieval or parsing error occurred during filtering.
func (it *NodesGovernanceNodeRegisteredIterator) Error() error {
	return it.fail
}

// Close terminates the iteration process, releasing any pending underlying
// resources.
func (it *NodesGovernanceNodeRegisteredIterator) Close() error {
	it.sub.Unsubscribe()
	return nil
}

// NodesGovernanceNodeRegistered represents a NodeRegistered event raised by the NodesGovernance contract.
type NodesGovernanceNodeRegistered struct {
	Wallet          common.Address
	Identifier      common.Address
	Time            *big.Int
	AliasIdentifier string
	Raw             types.Log // Blockchain specific contextual infos
}

// FilterNodeRegistered is a free log retrieval operation binding the contract event 0x0b464c895406310478ed7e414061d7621fd5f7444d7618e96d7fbb917a51f302.
//
// Solidity: event NodeRegistered(address indexed wallet, address identifier, uint256 time, string aliasIdentifier)
func (_NodesGovernance *NodesGovernanceFilterer) FilterNodeRegistered(opts *bind.FilterOpts, wallet []common.Address) (*NodesGovernanceNodeRegisteredIterator, error) {

	var walletRule []interface{}
	for _, walletItem := range wallet {
		walletRule = append(walletRule, walletItem)
	}

	logs, sub, err := _NodesGovernance.contract.FilterLogs(opts, "NodeRegistered", walletRule)
	if err != nil {
		return nil, err
	}
	return &NodesGovernanceNodeRegisteredIterator{contract: _NodesGovernance.contract, event: "NodeRegistered", logs: logs, sub: sub}, nil
}

// WatchNodeRegistered is a free log subscription operation binding the contract event 0x0b464c895406310478ed7e414061d7621fd5f7444d7618e96d7fbb917a51f302.
//
// Solidity: event NodeRegistered(address indexed wallet, address identifier, uint256 time, string aliasIdentifier)
func (_NodesGovernance *NodesGovernanceFilterer) WatchNodeRegistered(opts *bind.WatchOpts, sink chan<- *NodesGovernanceNodeRegistered, wallet []common.Address) (event.Subscription, error) {

	var walletRule []interface{}
	for _, walletItem := range wallet {
		walletRule = append(walletRule, walletItem)
	}

	logs, sub, err := _NodesGovernance.contract.WatchLogs(opts, "NodeRegistered", walletRule)
	if err != nil {
		return nil, err
	}
	return event.NewSubscription(func(quit <-chan struct{}) error {
		defer sub.Unsubscribe()
		for {
			select {
			case log := <-logs:
				// New log arrived, parse the event and forward to the user
				event := new(NodesGovernanceNodeRegistered)
				if err := _NodesGovernance.contract.UnpackLog(event, "NodeRegistered", log); err != nil {
					return err
				}
				event.Raw = log

				select {
				case sink <- event:
				case err := <-sub.Err():
					return err
				case <-quit:
					return nil
				}
			case err := <-sub.Err():
				return err
			case <-quit:
				return nil
			}
		}
	}), nil
}

// ParseNodeRegistered is a log parse operation binding the contract event 0x0b464c895406310478ed7e414061d7621fd5f7444d7618e96d7fbb917a51f302.
//
// Solidity: event NodeRegistered(address indexed wallet, address identifier, uint256 time, string aliasIdentifier)
func (_NodesGovernance *NodesGovernanceFilterer) ParseNodeRegistered(log types.Log) (*NodesGovernanceNodeRegistered, error) {
	event := new(NodesGovernanceNodeRegistered)
	if err := _NodesGovernance.contract.UnpackLog(event, "NodeRegistered", log); err != nil {
		return nil, err
	}
	event.Raw = log
	return event, nil
}

// NodesGovernanceSettlementResultIterator is returned from FilterSettlementResult and is used to iterate over the raw logs and unpacked data for SettlementResult events raised by the NodesGovernance contract.
type NodesGovernanceSettlementResultIterator struct {
	Event *NodesGovernanceSettlementResult // Event containing the contract specifics and raw log

	contract *bind.BoundContract // Generic contract to use for unpacking event data
	event    string              // Event name to use for unpacking event data

	logs chan types.Log        // Log channel receiving the found contract events
	sub  ethereum.Subscription // Subscription for errors, completion and termination
	done bool                  // Whether the subscription completed delivering logs
	fail error                 // Occurred error to stop iteration
}

// Next advances the iterator to the subsequent event, returning whether there
// are any more events found. In case of a retrieval or parsing error, false is
// returned and Error() can be queried for the exact failure.
func (it *NodesGovernanceSettlementResultIterator) Next() bool {
	// If the iterator failed, stop iterating
	if it.fail != nil {
		return false
	}
	// If the iterator completed, deliver directly whatever's available
	if it.done {
		select {
		case log := <-it.logs:
			it.Event = new(NodesGovernanceSettlementResult)
			if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
				it.fail = err
				return false
			}
			it.Event.Raw = log
			return true

		default:
			return false
		}
	}
	// Iterator still in progress, wait for either a data or an error event
	select {
	case log := <-it.logs:
		it.Event = new(NodesGovernanceSettlementResult)
		if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
			it.fail = err
			return false
		}
		it.Event.Raw = log
		return true

	case err := <-it.sub.Err():
		it.done = true
		it.fail = err
		return it.Next()
	}
}

// Error returns any retrieval or parsing error occurred during filtering.
func (it *NodesGovernanceSettlementResultIterator) Error() error {
	return it.fail
}

// Close terminates the iteration process, releasing any pending underlying
// resources.
func (it *NodesGovernanceSettlementResultIterator) Close() error {
	it.sub.Unsubscribe()
	return nil
}

// NodesGovernanceSettlementResult represents a SettlementResult event raised by the NodesGovernance contract.
type NodesGovernanceSettlementResult struct {
	States     []NodeState
	TotalQuota *big.Int
	Raw        types.Log // Blockchain specific contextual infos
}

// FilterSettlementResult is a free log retrieval operation binding the contract event 0xd51417935ddbb98970f20a5f6f9c5070ce90768d0e3bfaba49e7e2f8621debac.
//
// Solidity: event SettlementResult((uint64,uint64,uint128,address,address)[] states, uint256 totalQuota)
func (_NodesGovernance *NodesGovernanceFilterer) FilterSettlementResult(opts *bind.FilterOpts) (*NodesGovernanceSettlementResultIterator, error) {

	logs, sub, err := _NodesGovernance.contract.FilterLogs(opts, "SettlementResult")
	if err != nil {
		return nil, err
	}
	return &NodesGovernanceSettlementResultIterator{contract: _NodesGovernance.contract, event: "SettlementResult", logs: logs, sub: sub}, nil
}

// WatchSettlementResult is a free log subscription operation binding the contract event 0xd51417935ddbb98970f20a5f6f9c5070ce90768d0e3bfaba49e7e2f8621debac.
//
// Solidity: event SettlementResult((uint64,uint64,uint128,address,address)[] states, uint256 totalQuota)
func (_NodesGovernance *NodesGovernanceFilterer) WatchSettlementResult(opts *bind.WatchOpts, sink chan<- *NodesGovernanceSettlementResult) (event.Subscription, error) {

	logs, sub, err := _NodesGovernance.contract.WatchLogs(opts, "SettlementResult")
	if err != nil {
		return nil, err
	}
	return event.NewSubscription(func(quit <-chan struct{}) error {
		defer sub.Unsubscribe()
		for {
			select {
			case log := <-logs:
				// New log arrived, parse the event and forward to the user
				event := new(NodesGovernanceSettlementResult)
				if err := _NodesGovernance.contract.UnpackLog(event, "SettlementResult", log); err != nil {
					return err
				}
				event.Raw = log

				select {
				case sink <- event:
				case err := <-sub.Err():
					return err
				case <-quit:
					return nil
				}
			case err := <-sub.Err():
				return err
			case <-quit:
				return nil
			}
		}
	}), nil
}

// ParseSettlementResult is a log parse operation binding the contract event 0xd51417935ddbb98970f20a5f6f9c5070ce90768d0e3bfaba49e7e2f8621debac.
//
// Solidity: event SettlementResult((uint64,uint64,uint128,address,address)[] states, uint256 totalQuota)
func (_NodesGovernance *NodesGovernanceFilterer) ParseSettlementResult(log types.Log) (*NodesGovernanceSettlementResult, error) {
	event := new(NodesGovernanceSettlementResult)
	if err := _NodesGovernance.contract.UnpackLog(event, "SettlementResult", log); err != nil {
		return nil, err
	}
	event.Raw = log
	return event, nil
}

// NodesGovernanceValidationResultIterator is returned from FilterValidationResult and is used to iterate over the raw logs and unpacked data for ValidationResult events raised by the NodesGovernance contract.
type NodesGovernanceValidationResultIterator struct {
	Event *NodesGovernanceValidationResult // Event containing the contract specifics and raw log

	contract *bind.BoundContract // Generic contract to use for unpacking event data
	event    string              // Event name to use for unpacking event data

	logs chan types.Log        // Log channel receiving the found contract events
	sub  ethereum.Subscription // Subscription for errors, completion and termination
	done bool                  // Whether the subscription completed delivering logs
	fail error                 // Occurred error to stop iteration
}

// Next advances the iterator to the subsequent event, returning whether there
// are any more events found. In case of a retrieval or parsing error, false is
// returned and Error() can be queried for the exact failure.
func (it *NodesGovernanceValidationResultIterator) Next() bool {
	// If the iterator failed, stop iterating
	if it.fail != nil {
		return false
	}
	// If the iterator completed, deliver directly whatever's available
	if it.done {
		select {
		case log := <-it.logs:
			it.Event = new(NodesGovernanceValidationResult)
			if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
				it.fail = err
				return false
			}
			it.Event.Raw = log
			return true

		default:
			return false
		}
	}
	// Iterator still in progress, wait for either a data or an error event
	select {
	case log := <-it.logs:
		it.Event = new(NodesGovernanceValidationResult)
		if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
			it.fail = err
			return false
		}
		it.Event.Raw = log
		return true

	case err := <-it.sub.Err():
		it.done = true
		it.fail = err
		return it.Next()
	}
}

// Error returns any retrieval or parsing error occurred during filtering.
func (it *NodesGovernanceValidationResultIterator) Error() error {
	return it.fail
}

// Close terminates the iteration process, releasing any pending underlying
// resources.
func (it *NodesGovernanceValidationResultIterator) Close() error {
	it.sub.Unsubscribe()
	return nil
}

// NodesGovernanceValidationResult represents a ValidationResult event raised by the NodesGovernance contract.
type NodesGovernanceValidationResult struct {
	RoundId   *big.Int
	Validator common.Address
	Result    bool
	Raw       types.Log // Blockchain specific contextual infos
}

// FilterValidationResult is a free log retrieval operation binding the contract event 0x92de7c81b7cf6c7977d7cd091ccd01996264a02b7dbbce5d2a2524a8daabe769.
//
// Solidity: event ValidationResult(uint256 roundId, address validator, bool result)
func (_NodesGovernance *NodesGovernanceFilterer) FilterValidationResult(opts *bind.FilterOpts) (*NodesGovernanceValidationResultIterator, error) {

	logs, sub, err := _NodesGovernance.contract.FilterLogs(opts, "ValidationResult")
	if err != nil {
		return nil, err
	}
	return &NodesGovernanceValidationResultIterator{contract: _NodesGovernance.contract, event: "ValidationResult", logs: logs, sub: sub}, nil
}

// WatchValidationResult is a free log subscription operation binding the contract event 0x92de7c81b7cf6c7977d7cd091ccd01996264a02b7dbbce5d2a2524a8daabe769.
//
// Solidity: event ValidationResult(uint256 roundId, address validator, bool result)
func (_NodesGovernance *NodesGovernanceFilterer) WatchValidationResult(opts *bind.WatchOpts, sink chan<- *NodesGovernanceValidationResult) (event.Subscription, error) {

	logs, sub, err := _NodesGovernance.contract.WatchLogs(opts, "ValidationResult")
	if err != nil {
		return nil, err
	}
	return event.NewSubscription(func(quit <-chan struct{}) error {
		defer sub.Unsubscribe()
		for {
			select {
			case log := <-logs:
				// New log arrived, parse the event and forward to the user
				event := new(NodesGovernanceValidationResult)
				if err := _NodesGovernance.contract.UnpackLog(event, "ValidationResult", log); err != nil {
					return err
				}
				event.Raw = log

				select {
				case sink <- event:
				case err := <-sub.Err():
					return err
				case <-quit:
					return nil
				}
			case err := <-sub.Err():
				return err
			case <-quit:
				return nil
			}
		}
	}), nil
}

// ParseValidationResult is a log parse operation binding the contract event 0x92de7c81b7cf6c7977d7cd091ccd01996264a02b7dbbce5d2a2524a8daabe769.
//
// Solidity: event ValidationResult(uint256 roundId, address validator, bool result)
func (_NodesGovernance *NodesGovernanceFilterer) ParseValidationResult(log types.Log) (*NodesGovernanceValidationResult, error) {
	event := new(NodesGovernanceValidationResult)
	if err := _NodesGovernance.contract.UnpackLog(event, "ValidationResult", log); err != nil {
		return nil, err
	}
	event.Raw = log
	return event, nil
}

// NodesGovernanceValidationStartedIterator is returned from FilterValidationStarted and is used to iterate over the raw logs and unpacked data for ValidationStarted events raised by the NodesGovernance contract.
type NodesGovernanceValidationStartedIterator struct {
	Event *NodesGovernanceValidationStarted // Event containing the contract specifics and raw log

	contract *bind.BoundContract // Generic contract to use for unpacking event data
	event    string              // Event name to use for unpacking event data

	logs chan types.Log        // Log channel receiving the found contract events
	sub  ethereum.Subscription // Subscription for errors, completion and termination
	done bool                  // Whether the subscription completed delivering logs
	fail error                 // Occurred error to stop iteration
}

// Next advances the iterator to the subsequent event, returning whether there
// are any more events found. In case of a retrieval or parsing error, false is
// returned and Error() can be queried for the exact failure.
func (it *NodesGovernanceValidationStartedIterator) Next() bool {
	// If the iterator failed, stop iterating
	if it.fail != nil {
		return false
	}
	// If the iterator completed, deliver directly whatever's available
	if it.done {
		select {
		case log := <-it.logs:
			it.Event = new(NodesGovernanceValidationStarted)
			if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
				it.fail = err
				return false
			}
			it.Event.Raw = log
			return true

		default:
			return false
		}
	}
	// Iterator still in progress, wait for either a data or an error event
	select {
	case log := <-it.logs:
		it.Event = new(NodesGovernanceValidationStarted)
		if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
			it.fail = err
			return false
		}
		it.Event.Raw = log
		return true

	case err := <-it.sub.Err():
		it.done = true
		it.fail = err
		return it.Next()
	}
}

// Error returns any retrieval or parsing error occurred during filtering.
func (it *NodesGovernanceValidationStartedIterator) Error() error {
	return it.fail
}

// Close terminates the iteration process, releasing any pending underlying
// resources.
func (it *NodesGovernanceValidationStartedIterator) Close() error {
	it.sub.Unsubscribe()
	return nil
}

// NodesGovernanceValidationStarted represents a ValidationStarted event raised by the NodesGovernance contract.
type NodesGovernanceValidationStarted struct {
	RoundId                *big.Int
	ExpectedCompletionTime *big.Int
	Candidate              common.Address
	Validators             []common.Address
	Raw                    types.Log // Blockchain specific contextual infos
}

// FilterValidationStarted is a free log retrieval operation binding the contract event 0x71afff60b83105500984ce43d4633544224775a10de240da021704c056b58bdb.
//
// Solidity: event ValidationStarted(uint256 roundId, uint256 expectedCompletionTime, address candidate, address[] validators)
func (_NodesGovernance *NodesGovernanceFilterer) FilterValidationStarted(opts *bind.FilterOpts) (*NodesGovernanceValidationStartedIterator, error) {

	logs, sub, err := _NodesGovernance.contract.FilterLogs(opts, "ValidationStarted")
	if err != nil {
		return nil, err
	}
	return &NodesGovernanceValidationStartedIterator{contract: _NodesGovernance.contract, event: "ValidationStarted", logs: logs, sub: sub}, nil
}

// WatchValidationStarted is a free log subscription operation binding the contract event 0x71afff60b83105500984ce43d4633544224775a10de240da021704c056b58bdb.
//
// Solidity: event ValidationStarted(uint256 roundId, uint256 expectedCompletionTime, address candidate, address[] validators)
func (_NodesGovernance *NodesGovernanceFilterer) WatchValidationStarted(opts *bind.WatchOpts, sink chan<- *NodesGovernanceValidationStarted) (event.Subscription, error) {

	logs, sub, err := _NodesGovernance.contract.WatchLogs(opts, "ValidationStarted")
	if err != nil {
		return nil, err
	}
	return event.NewSubscription(func(quit <-chan struct{}) error {
		defer sub.Unsubscribe()
		for {
			select {
			case log := <-logs:
				// New log arrived, parse the event and forward to the user
				event := new(NodesGovernanceValidationStarted)
				if err := _NodesGovernance.contract.UnpackLog(event, "ValidationStarted", log); err != nil {
					return err
				}
				event.Raw = log

				select {
				case sink <- event:
				case err := <-sub.Err():
					return err
				case <-quit:
					return nil
				}
			case err := <-sub.Err():
				return err
			case <-quit:
				return nil
			}
		}
	}), nil
}

// ParseValidationStarted is a log parse operation binding the contract event 0x71afff60b83105500984ce43d4633544224775a10de240da021704c056b58bdb.
//
// Solidity: event ValidationStarted(uint256 roundId, uint256 expectedCompletionTime, address candidate, address[] validators)
func (_NodesGovernance *NodesGovernanceFilterer) ParseValidationStarted(log types.Log) (*NodesGovernanceValidationStarted, error) {
	event := new(NodesGovernanceValidationStarted)
	if err := _NodesGovernance.contract.UnpackLog(event, "ValidationStarted", log); err != nil {
		return nil, err
	}
	event.Raw = log
	return event, nil
}
