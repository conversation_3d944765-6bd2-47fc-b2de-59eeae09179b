package util

import (
	"github.com/jaypipes/ghw"
)

type GPUInfo struct {
	Name     string
	Vendor   string
	Quantity int
}

func GetGPUInfo() (map[string]GPUInfo, error) {
	gpu, err := ghw.GPU()
	if err != nil {
		return nil, err
	}

	gpuInfos := make(map[string]GPUInfo)
	// vendorRegex := regexp.MustCompile(`(?i)nvidia|amd|intel`)

	for _, card := range gpu.GraphicsCards {
		// matches := vendorRegex.FindAllString(card.DeviceInfo.Vendor.Name, -1)
		// if len(matches) <= 0 {
		// 	continue
		// }

		if info, exists := gpuInfos[card.DeviceInfo.Product.Name]; exists {
			info.Quantity++
			gpuInfos[card.DeviceInfo.Product.Name] = info
		} else {
			gpuInfos[card.DeviceInfo.Product.Name] = GPUInfo{
				Name:     card.DeviceInfo.Product.Name,
				Vendor:   card.DeviceInfo.Vendor.Name,
				Quantity: 1,
			}
		}
	}

	return gpuInfos, nil
}
