package chain

import (
	"context"
	"crypto/ecdsa"
	"fmt"
	"math/big"
	"strings"
	"time"

	"github.com/ethereum/go-ethereum"
	"github.com/ethereum/go-ethereum/accounts/abi"
	"github.com/ethereum/go-ethereum/accounts/abi/bind"
	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/core/types"
	"github.com/ethereum/go-ethereum/crypto"
	"github.com/ethereum/go-ethereum/ethclient"
	logging "github.com/ipfs/go-log/v2"
	"uptech.ai/ainode/abi/nodesgovernance"
	"uptech.ai/ainode/config"
	"uptech.ai/ainode/util"
)

var (
	logger  = logging.Logger("chain")
	timeout = time.Second * 15
)

type ChainClient struct {
	url             string
	wallet          common.Address
	identifier      common.Address
	contract        common.Address
	chainId         int
	aliasIdentifier string
	fromHeight      *big.Int
	stake           *big.Int
}

func MakeChainClient(ctx context.Context, config *config.Config) (ChainClient, error) {
	contractAddress := common.HexToAddress(config.Contract)
	privateKey, err := crypto.HexToECDSA(config.WalletKey)
	if err != nil {
		logger.Info("Failed to parse key")
		return ChainClient{}, err
	}
	defer privateKey.D.Set(big.NewInt(0))

	publicKey := privateKey.Public()
	publicKeyECDSA, ok := publicKey.(*ecdsa.PublicKey)
	if !ok {
		logger.Info("Failed to cast public key to ECDSA")
		return ChainClient{}, err
	}

	identifier := crypto.PubkeyToAddress(*publicKeyECDSA)

	return ChainClient{
		url:             config.ChainUrl,
		contract:        contractAddress,
		wallet:          identifier,
		identifier:      identifier,
		chainId:         config.ChainID,
		aliasIdentifier: config.Id,
		fromHeight:      big.NewInt(config.FromHeight),
		stake:           big.NewInt(config.Stake),
	}, nil
}

func (chain *ChainClient) confirmTx(ctx context.Context, client *ethclient.Client, tx *types.Transaction) error {
	timeoutCtx, cancel := context.WithTimeout(ctx, timeout)
	_, err := client.TransactionReceipt(timeoutCtx, tx.Hash())
	cancel()
	if err != nil {
		logger.Errorf("Failed to confirmTx: %v", err)
		return err
	}

	timeoutCtx, cancel = context.WithTimeout(ctx, timeout)
	latestNonce, err := client.NonceAt(timeoutCtx, chain.identifier, nil)
	cancel()
	if err != nil {
		logger.Errorf("Failed to fetch nonce: %v", err)
		return err
	}

	if tx.Nonce() < latestNonce {
		logger.Errorf("Transaction nonce is too low, possible replacement or duplicate.")
		return fmt.Errorf("Transaction nonce is too low, possible replacement or duplicate.")
	}

	return nil
}

func (chain *ChainClient) Run(ctx context.Context, walletKey string) error {
	privateKey, err := crypto.HexToECDSA(walletKey)
	if err != nil {
		logger.Error("Failed to parse key")
		return err
	}

	opts, err := bind.NewKeyedTransactorWithChainID(privateKey, big.NewInt(int64(chain.chainId)))
	if err != nil {
		logger.Errorf("Failed to create transactor: %v", err)
		return err
	}

	// opts.GasFeeCap = big.NewInt(2_000_000_000)
	// opts.GasTipCap = big.NewInt(0)

	go func() {
		errorChan := make(chan error)
		for {
			if err = chain.run(ctx, errorChan, opts); err != nil {
				logger.Errorf("run error %v", err)
			}

			time.Sleep(500 * time.Millisecond)
		}
	}()

	return nil
}

func (chain *ChainClient) run(ctx context.Context, errCh chan error, opts *bind.TransactOpts) error {
	client, err := chain.connect(ctx)
	if err != nil {
		return err
	}
	defer client.Close()

	contract, err := nodesgovernance.NewNodesGovernance(chain.contract, client)
	if err != nil {
		logger.Errorf("Failed to instantiate the contract: %v", err)
		return err
	}

	if err = chain.registerNode(ctx, contract, opts, client); err != nil {
		return err
	}

	if err = chain.processEvent(ctx, client, contract, opts, errCh); err != nil {
		return err
	}

	err = <-errCh

	return err
}

func (chain *ChainClient) connect(ctx context.Context) (*ethclient.Client, error) {
	ctxTimeout, cancel := context.WithTimeout(ctx, timeout)
	defer cancel()
	client, err := ethclient.DialContext(ctxTimeout, chain.url)
	if err != nil {
		logger.Infof("Fail to dail %s", chain.url)
		return nil, err
	}

	return client, nil
}

func (chain *ChainClient) registerNode(
	ctx context.Context,
	contract *nodesgovernance.NodesGovernance,
	opts *bind.TransactOpts,
	client *ethclient.Client,
) error {
	ctxTimeout, cancel := context.WithTimeout(ctx, timeout)
	callOpts := &bind.CallOpts{
		Pending: false,
		From:    chain.identifier,
		Context: ctxTimeout,
	}

	node, err := contract.Get(callOpts, chain.identifier)
	cancel()
	if err != nil {
		logger.Errorf("Fail to get node infomation %v", err)
		return err
	}

	if node.Identifier != common.HexToAddress("******************************************") {
		logger.Debugf("node identifier %v is exist", node.Identifier)
		return nil
	}

	gpuTypes := []string{}
	gpuNums := []*big.Int{}

	if gpus, err := util.GetGPUInfo(); err != nil {
		logger.Errorf("Fail to get gpu infomation %v", err)
		return err
	} else {
		if len(gpus) == 0 {
			logger.Info("None gpu infomation")
			return fmt.Errorf("None gpu infomation")
		}

		for _, v := range gpus {
			logger.Infof("gpu name:%s, vendor:%s, count:%v", v.Name, v.Vendor, v.Quantity)
			gpuTypes = append(gpuTypes, v.Name)
			gpuNums = append(gpuNums, big.NewInt(int64(v.Quantity)))
		}

		gpus = nil
	}

	opts.Context, cancel = context.WithTimeout(ctx, timeout)
	opts.Value = chain.stake
	tx, err := contract.RegisterNode(opts, chain.wallet, chain.aliasIdentifier, gpuTypes, gpuNums)
	cancel()
	chain.stake = nil
	if err != nil {
		logger.Errorf("Failed to register node: %v", err)
		return err
	}

	err = chain.confirmTx(ctx, client, tx)
	if err != nil {
		return err
	}

	logger.Debugf("register successful, tx:%v", tx.Hash().Hex())
	return nil
}

func (chain *ChainClient) processEvent(
	ctx context.Context,
	client *ethclient.Client,
	contract *nodesgovernance.NodesGovernance,
	opts *bind.TransactOpts,
	errChan chan<- error,
) error {
	parsedABI, err := abi.JSON(strings.NewReader(nodesgovernance.NodesGovernanceABI))
	if err != nil {
		logger.Errorf("Failed to parse ABI: %v", err)
		return err
	}
	events := []string{"NodeRegistered", "NodeDeregistered", "NodeActived", "ValidationStarted"}
	eventIDs := []common.Hash{}
	for _, eventName := range events {
		eventABI, exists := parsedABI.Events[eventName]
		if !exists {
			return fmt.Errorf("method '%s' not found", eventName)
		}
		eventIDs = append(eventIDs, eventABI.ID)
	}

	go func() {
		for {
			ctxTimeout, cancel := context.WithTimeout(ctx, timeout)
			newHeader, err := client.HeaderByNumber(ctxTimeout, nil)
			cancel()
			if err != nil {
				errChan <- err
				logger.Errorf("Failed get header number: %v", err)
				return
			}
			if newHeader.Number.Cmp(chain.fromHeight) < 0 {
				time.Sleep(1000 * time.Millisecond)
				continue
			}

			query := ethereum.FilterQuery{
				Addresses: []common.Address{chain.contract},
				FromBlock: chain.fromHeight,
				ToBlock:   chain.fromHeight,
				Topics: [][]common.Hash{
					eventIDs,
				}}

			logger.Debugf("height: %v, new height: %v", chain.fromHeight, newHeader.Number)
			ctxTimeout, cancel = context.WithTimeout(ctx, timeout)
			logs, err := client.FilterLogs(ctxTimeout, query)
			cancel()
			if err != nil {
				logger.Errorf("Failed to fetch logs: %v", err)
				errChan <- err
				return
			}

			for _, vLog := range logs {
				logger.Debugf("Event log received! Block number: %v,Tx hash: %v", vLog.BlockNumber, vLog.TxHash.Hex())
				if event, err := contract.ParseNodeRegistered(vLog); err == nil {
					logger.Debugf("event: node registered, Wallet: %s, Alias Identifier: %s", event.Wallet.Hex(), event.AliasIdentifier)
				} else if event, err := contract.ParseNodeDeregistered(vLog); err == nil {
					logger.Debugf("event: node deregistered, Identifier: %s, Alias Identifier: %s", event.Identifier.Hex(), event.AliasIdentifier)
				} else if event, err := contract.ParseValidationStarted(vLog); err == nil {
					logger.Debugf("event: node validation started, Candidate: %s", event.Candidate.Hex())
					for _, validator := range event.Validators {
						if validator.Cmp(chain.identifier) != 0 {
							continue
						}

						// opts.Context, cancel = context.WithTimeout(ctx, timeout)
						// validators, err := contract.GetValidatorsOfCandidate(opts, event.RoundId, event.Candidate)
						opts.Context, cancel = context.WithTimeout(ctx, timeout)
						result, err := contract.Vote(opts, event.RoundId, event.Candidate, true)
						cancel()
						if err != nil {
							logger.Errorf("Failed to vote: %v", err)
							if !strings.Contains(err.Error(), "execution reverted") {
								errChan <- err
								return
							} else {
								break
							}
						} else {
							logger.Debugf("vote, tx: %v", result.Hash())
						}

						err = chain.confirmTx(ctx, client, result)
						if err != nil {
							errChan <- err
							return
						}

						break
					}
				} else if event, err := contract.ParseNodeActived(vLog); err == nil {
					logger.Debugf("event: node actived, Identifier: %s, Alias Identifier: %s", event.Identifier.Hex(), event.AliasIdentifier)
				}
			}
			chain.fromHeight = chain.fromHeight.Add(chain.fromHeight, big.NewInt(1))
		}
	}()

	return nil
}
