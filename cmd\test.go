package main

import "fmt"

func main() {
	assets := []int{1, 100, 1213, 13, 54, 67, 12100, 12121, 100, 1, 112}
	a := calculateSteal(0, len(assets)-1, assets)
	fmt.Println("max value is", a)

	b := calculateSteps(100)
	fmt.Println("steps is", b)

	c := subsequence([]int{-1, -2, -3, -4})
	fmt.Println("steps is", c)
}

func calculateSteal(start, end int, assets []int) int {
	if start > end {
		return 0
	}

	a := assets[start] + calculateSteal(start+2, end, assets)
	b := calculateSteal(start+1, end, assets)

	if a > b {
		return a
	}
	return b
}

func calculateSteps(n uint) int {
	if n == 0 {
		return 0
	} else if n == 1 {
		return 1
	} else if n == 2 {
		return 2
	}

	f0 := 1
	f1 := 2
	for i := uint(3); i <= n; i++ {
		temp := f1
		f1 = f0 + f1
		f0 = temp
	}

	return f1
}

func subsequence(assets []int) int {
	max := assets[0]
	endMax := assets[0]

	for i := 1; i < len(assets); i++ {
		if endMax < 0 {
			endMax = assets[i]
		} else {
			endMax = endMax + assets[i]
		}

		if endMax > max {
			max = endMax
		}
	}

	return max
}
