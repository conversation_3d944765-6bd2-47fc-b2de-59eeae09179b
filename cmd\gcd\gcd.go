package main

import (
	"fmt"
)

// 递归实现辗转相除法
func gcdRecursive(a, b int) int {
	if b == 0 {
		return a
	}
	return gcdRecursive(b, a%b)
}

// 迭代实现辗转相除法
func gcdIterative(a, b int) int {
	for b != 0 {
		temp := b
		b = a % b
		a = temp
	}
	return a
}

// 处理负数的情况
func gcd(a, b int) int {
	// 处理负数，取绝对值
	if a < 0 {
		a = -a
	}
	if b < 0 {
		b = -b
	}

	// 确保a >= b
	if a < b {
		a, b = b, a
	}

	return gcdRecursive(a, b)
}

// 求多个数的最大公约数
func gcdMultiple(nums ...int) int {
	if len(nums) == 0 {
		return 0
	}
	if len(nums) == 1 {
		return nums[0]
	}

	result := gcd(nums[0], nums[1])
	for i := 2; i < len(nums); i++ {
		result = gcd(result, nums[i])
	}
	return result
}

func main() {
	// 测试用例
	fmt.Println("=== 辗转相除法求最大公约数 ===")

	// 基本测试
	a, b := 1080, 1215
	fmt.Printf("gcd(%d, %d) = %d (递归)\n", a, b, gcdRecursive(a, b))
	fmt.Printf("gcd(%d, %d) = %d (迭代)\n", a, b, gcdIterative(a, b))

	// 更多测试用例
	testCases := [][2]int{
		{56, 42},
		{17, 13},
		{100, 25},
		{1071, 462},
		{0, 5},
		{7, 0},
	}

	fmt.Println("\n=== 更多测试用例 ===")
	for _, tc := range testCases {
		result := gcd(tc[0], tc[1])
		fmt.Printf("gcd(%d, %d) = %d\n", tc[0], tc[1], result)
	}

	// 多个数的最大公约数
	fmt.Println("\n=== 多个数的最大公约数 ===")
	nums := []int{24, 36, 48, 60}
	result := gcdMultiple(nums...)
	fmt.Printf("gcd(%v) = %d\n", nums, result)

	// 带负数的情况
	fmt.Println("\n=== 处理负数 ===")
	fmt.Printf("gcd(-48, 18) = %d\n", gcd(-48, 18))
	fmt.Printf("gcd(48, -18) = %d\n", gcd(48, -18))
	fmt.Printf("gcd(-48, -18) = %d\n", gcd(-48, -18))
}
